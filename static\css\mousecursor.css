/*! normalize.css v7.0.0 | MIT License | github.com/necolas/normalize.css */
.cb-cursor {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 150;
  contain: layout style size;
  pointer-events: none;
  will-change: transform;
  -webkit-transition: opacity 0.3s, color 0.4s;
  -o-transition: opacity 0.3s, color 0.4s;
  -moz-transition: opacity 0.3s, color 0.4s;
  transition: opacity 0.3s, color 0.4s;
}

.cb-cursor:before {
  content: "";
  position: absolute;
  top: -24px;
  left: -24px;
  display: block;
  width: 48px;
  height: 48px;
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  border-radius: 50%;
  -webkit-transition: opacity 0.1s, -webkit-transform 0.3s ease-in-out;
  transition: opacity 0.1s, -webkit-transform 0.3s ease-in-out;
  -o-transition: opacity 0.1s, -o-transform 0.3s ease-in-out;
  -moz-transition: transform 0.3s ease-in-out, opacity 0.1s,
    -moz-transform 0.3s ease-in-out;
  transition: transform 0.3s ease-in-out, opacity 0.1s;
  transition: transform 0.3s ease-in-out, opacity 0.1s,
    -webkit-transform 0.3s ease-in-out, -moz-transform 0.3s ease-in-out,
    -o-transform 0.3s ease-in-out;
}

.cb-cursor-text {
  position: absolute;
  top: -18px;
  left: -18px;
  width: 36px;
  height: 36px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-transform: scale(0) rotate(10deg);
  -moz-transform: scale(0) rotate(10deg);
  -ms-transform: scale(0) rotate(10deg);
  -o-transform: scale(0) rotate(10deg);
  transform: scale(0) rotate(10deg);
  opacity: 0;
  color: white;
  font-size: 16px;
  line-height: 20px;
  text-align: center;
  letter-spacing: -0.01em;
  -webkit-transition: opacity 0.4s, -webkit-transform 0.3s;
  transition: opacity 0.4s, -webkit-transform 0.3s;
  -o-transition: opacity 0.4s, -o-transform 0.3s;
  -moz-transition: opacity 0.4s, transform 0.3s, -moz-transform 0.3s;
  transition: opacity 0.4s, transform 0.3s;
  transition: opacity 0.4s, transform 0.3s, -webkit-transform 0.3s,
    -moz-transform 0.3s, -o-transform 0.3s;
}

@supports (mix-blend-mode: exclusion) {
  .cb-cursor.-exclusion,
  .cb-cursor.-opaque {
    mix-blend-mode: exclusion;
  }
}

@supports (mix-blend-mode: exclusion) {
  .cb-cursor.-exclusion:before,
  .cb-cursor.-opaque:before {
    background: white;
  }
}

.cb-cursor.-normal,
.cb-cursor.-text {
  mix-blend-mode: normal;
}

.cb-cursor.-normal:before,
.cb-cursor.-text:before {
  background: currentColor;
}

.cb-cursor.-inverse {
  color: white;
}

.cb-cursor.-visible:before {
  -webkit-transform: scale(0.2);
  -moz-transform: scale(0.2);
  -ms-transform: scale(0.2);
  -o-transform: scale(0.2);
  transform: scale(0.2);
}

.cb-cursor.-visible.-active:before {
  -webkit-transform: scale(0.23);
  -moz-transform: scale(0.23);
  -ms-transform: scale(0.23);
  -o-transform: scale(0.23);
  transform: scale(0.23);
  -webkit-transition-duration: 0.2s;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  transition-duration: 0.2s;
}

.cb-cursor.-pointer:before {
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}

.cb-cursor.-text:before {
  background: #000;
  opacity: 0.8;
  backdrop-filter: blur(10);
  -webkit-transform: scale(1.7);
  -moz-transform: scale(1.7);
  -ms-transform: scale(1.7);
  -o-transform: scale(1.7);
  transform: scale(1.7);
}

.cb-cursor.-text .cb-cursor-text {
  opacity: 1;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.cb-cursor.-text.-active:before {
  -webkit-transform: scale(1.6);
  -moz-transform: scale(1.6);
  -ms-transform: scale(1.6);
  -o-transform: scale(1.6);
  transform: scale(1.6);
  -webkit-transition-duration: 0.2s;
  -moz-transition-duration: 0.2s;
  -o-transition-duration: 0.2s;
  transition-duration: 0.2s;
}

.cb-cursor.-opaque:before {
  -webkit-transform: scale(1.32);
  -moz-transform: scale(1.32);
  -ms-transform: scale(1.32);
  -o-transform: scale(1.32);
  transform: scale(1.32);
}

.cb-cursor.-opaque.-active:before {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

.cb-cursor.-lg:before {
  -webkit-transform: scale(2);
  -moz-transform: scale(2);
  -ms-transform: scale(2);
  -o-transform: scale(2);
  transform: scale(2);
}

.cb-cursor.-hidden:before {
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}

.-color-red {
  color: red;
}

.-color-green {
  color: #51c67d;
}

.cb-demo {
  background: #fff;
}

.cb-demo-content {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -moz-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100vh;
}

.cb-demo-container {
  padding: 0 20px;
}

@media (min-width: 1600px) {
  .cb-demo-container {
    padding: 0 120px;
  }
}

.cb-demo-row {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  margin: 30px 0;
}

@media (min-width: 1600px) {
  .cb-demo-row {
    margin: 60px 0;
  }
}

.cb-demo-item {
  position: relative;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -moz-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding: 50px 30px;
  margin: 0 20px;
  color: #000;
}

@media (min-width: 1600px) {
  .cb-demo-item {
    padding: 90px 30px;
    margin: 0 30px;
  }
}

.cb-demo-item-title {
  position: relative;
  margin: 0 0 25px 0;
  font-size: 30px;
  font-weight: bold;
}

.cb-demo-item-text {
  position: relative;
  max-width: 70%;
  margin: 0 auto;
  color: rgba(0, 0, 0, 0.5);
  font-size: 16px;
  font-weight: 300;
  line-height: 150%;
}

.cb-demo-item:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  border-radius: 30px;
  background: #f8f8f8;
  -webkit-transition: -webkit-box-shadow 0.2s;
  transition: -webkit-box-shadow 0.2s;
  -o-transition: box-shadow 0.2s;
  -moz-transition: box-shadow 0.2s, -moz-box-shadow 0.2s;
  transition: box-shadow 0.2s;
  transition: box-shadow 0.2s, -webkit-box-shadow 0.2s, -moz-box-shadow 0.2s;
}

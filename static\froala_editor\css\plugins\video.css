/*!
 * froala_editor v4.0.10 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

.fr-clearfix::after {
  clear: both;
  display: block;
  content: "";
  height: 0; }

.fr-hide-by-clipping {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0; }

.fr-element .fr-video {
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none; }
  .fr-element .fr-video::after {
    position: absolute;
    content: '';
    z-index: 1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    cursor: pointer;
    display: block;
    background: rgba(0, 0, 0, 0); }
  .fr-element .fr-video.fr-active > * {
    z-index: 2;
    position: relative; }
  .fr-element .fr-video > * {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
    max-width: 100%;
    border: none; }

.fr-box .fr-video-resizer {
  position: absolute;
  border: solid 1px #0098f7;
  display: none;
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none; }
  .fr-box .fr-video-resizer.fr-active {
    display: block; }
  .fr-box .fr-video-resizer .fr-handler {
    display: block;
    position: absolute;
    background: #0098f7;
    border: solid 1px #FFF;
    z-index: 4;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box; }
    .fr-box .fr-video-resizer .fr-handler.fr-hnw {
      cursor: nw-resize; }
    .fr-box .fr-video-resizer .fr-handler.fr-hne {
      cursor: ne-resize; }
    .fr-box .fr-video-resizer .fr-handler.fr-hsw {
      cursor: sw-resize; }
    .fr-box .fr-video-resizer .fr-handler.fr-hse {
      cursor: se-resize; }
  .fr-box .fr-video-resizer .fr-handler {
    width: 12px;
    height: 12px; }
    .fr-box .fr-video-resizer .fr-handler.fr-hnw {
      left: -6px;
      top: -6px; }
    .fr-box .fr-video-resizer .fr-handler.fr-hne {
      right: -6px;
      top: -6px; }
    .fr-box .fr-video-resizer .fr-handler.fr-hsw {
      left: -6px;
      bottom: -6px; }
    .fr-box .fr-video-resizer .fr-handler.fr-hse {
      right: -6px;
      bottom: -6px; }
  @media (min-width: 1200px) {
    .fr-box .fr-video-resizer .fr-handler {
      width: 10px;
      height: 10px; }
      .fr-box .fr-video-resizer .fr-handler.fr-hnw {
        left: -5px;
        top: -5px; }
      .fr-box .fr-video-resizer .fr-handler.fr-hne {
        right: -5px;
        top: -5px; }
      .fr-box .fr-video-resizer .fr-handler.fr-hsw {
        left: -5px;
        bottom: -5px; }
      .fr-box .fr-video-resizer .fr-handler.fr-hse {
        right: -5px;
        bottom: -5px; } }

.fr-popup .fr-video-size-layer .fr-video-group .fr-input-line {
  width: calc(50% - 5px);
  display: inline-block; }
  .fr-popup .fr-video-size-layer .fr-video-group .fr-input-line + .fr-input-line {
    margin-left: 10px; }
.fr-popup .fr-video-upload-layer {
  border: dashed 2px #bdbdbd;
  padding: 25px 0;
  margin: 20px;
  position: relative;
  font-size: 14px;
  letter-spacing: 1px;
  line-height: 140%;
  text-align: center; }
  .fr-popup .fr-video-upload-layer:hover {
    background: #ebebeb; }
  .fr-popup .fr-video-upload-layer.fr-drop {
    background: #ebebeb;
    border-color: #0098f7; }
  .fr-popup .fr-video-upload-layer .fr-form {
    -webkit-opacity: 0;
    -moz-opacity: 0;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2147483640;
    overflow: hidden;
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important; }
    .fr-popup .fr-video-upload-layer .fr-form input {
      cursor: pointer;
      position: absolute;
      right: 0;
      top: 0;
      bottom: 0;
      width: 500%;
      height: 100%;
      margin: 0;
      font-size: 400px; }
.fr-popup .fr-video-progress-bar-layer > h3 {
  font-size: 16px;
  margin: 10px 0;
  font-weight: normal; }
.fr-popup .fr-video-progress-bar-layer > div.fr-action-buttons {
  display: none; }
.fr-popup .fr-video-progress-bar-layer > div.fr-loader {
  background: #b3e0fd;
  height: 10px;
  width: 100%;
  margin-top: 20px;
  overflow: hidden;
  position: relative; }
  .fr-popup .fr-video-progress-bar-layer > div.fr-loader span {
    display: block;
    height: 100%;
    width: 0%;
    background: #0098f7;
    -webkit-transition: width 0.2s ease 0s;
    -moz-transition: width 0.2s ease 0s;
    -ms-transition: width 0.2s ease 0s;
    -o-transition: width 0.2s ease 0s; }
  .fr-popup .fr-video-progress-bar-layer > div.fr-loader.fr-indeterminate span {
    width: 30% !important;
    position: absolute;
    top: 0;
    -webkit-animation: loading 2s linear infinite;
    -moz-animation: loading 2s linear infinite;
    -o-animation: loading 2s linear infinite;
    animation: loading 2s linear infinite; }
.fr-popup .fr-video-progress-bar-layer.fr-error > div.fr-loader {
  display: none; }
.fr-popup .fr-video-progress-bar-layer.fr-error > div.fr-action-buttons {
  display: block; }

.fr-video-overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 2147483640;
  display: none; }

.fr-autoplay-margin {
  margin-top: 0px !important; }

/*!
 * froala_editor v4.0.10 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

.fr-clearfix::after {
  clear: both;
  display: block;
  content: "";
  height: 0; }

.fr-hide-by-clipping {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0; }

.fr-wrapper-markdown {
  display: flex; }

.fr-markdown-view {
  width: calc(52% - 1px);
  box-sizing: inherit; }

.fr-markdown-editor {
  width: calc(48% - 1px);
  box-sizing: inherit;
  background-color: #eee; }

.fr-markdown-editor > p {
  margin: 0; }

.fr-markdown-view > p {
  margin-top: 0; }

.gutter-horizontal {
  display: flex;
  z-index: 9;
  background-color: #dadada;
  cursor: col-resize;
  width: 2px; }

.e-resize-handler {
  z-index: 9;
  width: 1px;
  display: flex;
  justify-content: center;
  align-items: center;
  align-self: center;
  position: relative;
  font-size: 10px;
  color: #c5c5c5; }

.fr-markdown-view > dl {
  margin-top: 0;
  margin-bottom: 1rem; }

.fr-markdown-view > dt {
  font-weight: 700; }

.fr-markdown-view > dd {
  margin-bottom: .5rem;
  margin-left: 0; }

.fr-markdown-view > pre {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
  background-color: #f8f8f8;
  border: 1px solid #dfdfdf;
  margin-top: 1.5em;
  margin-bottom: 1.5em;
  padding: 0.125rem 0.3125rem 0.0625rem; }

.fr-markdown-view > code {
  background-color: #f8f8f8;
  border-color: #dfdfdf;
  border-style: solid;
  border-width: 1px;
  color: #333;
  font-family: Consolas,"Liberation Mono",Courier,monospace;
  font-weight: normal;
  padding: 0.125rem 0.3125rem 0.0625rem; }

.fr-markdown-view > pre code {
  background-color: transparent;
  border: 0;
  padding: 0; }

.fr-markdown-view > sup {
  top: -.5em; }

.footnote-a {
  color: #007bff;
  text-decoration: none;
  background-color: transparent; }

.fr-markdown-view > hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1); }

.blockquote {
  margin: 0 0 1rem;
  border-left: 5px solid #eee;
  padding: 10px 20px;
  font-size: 1.25rem; }

.fr-markdown-view > table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 20px;
  background-color: transparent;
  border-spacing: 0;
  border-collapse: collapse; }

.fr-markdown-view > table > tbody > tr > td,
.fr-markdown-view > table > tbody > tr > th,
.fr-markdown-view > table > thead > tr > td,
.fr-markdown-view > table > thead > tr > th {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border: 1px solid #ddd; }

.fr-markdown-view > table > thead > tr > td,
.fr-markdown-view > table > thead > tr > th {
  border-bottom-width: 2px; }

.fr-markdown-view > table > tbody > tr:nth-of-type(odd) {
  background-color: #f9f9f9; }

.fr-markdown-view > a {
  color: #337ab7;
  text-decoration: none; }

.fr-markdown-view > h1 {
  font-size: 2em !important; }

.fr-markdown-view > h2 {
  font-size: 1.5em !important; }

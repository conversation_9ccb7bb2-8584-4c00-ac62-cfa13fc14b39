/*!
 * froala_editor v4.0.10 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(ve){"use strict";ve=ve&&ve.hasOwnProperty("default")?ve["default"]:ve,Object.assign(ve.POPUP_TEMPLATES,{"table.insert":"[_BUTTONS_][_ROWS_COLUMNS_]","table.edit":"[_BUTTONS_]","table.colors":"[_BUTTONS_][_COLORS_][_CUSTOM_COLOR_]"}),Object.assign(ve.DEFAULTS,{tableInsertMaxSize:10,tableEditButtons:["tableHeader","tableRemove","tableRows","tableColumns","tableStyle","-","tableCells","tableCellBackground","tableCellVerticalAlign","tableCellHorizontalAlign","tableCellStyle"],tableInsertButtons:["tableBack","|"],tableResizer:!0,tableDefaultWidth:"100%",tableResizerOffset:5,tableResizingLimit:30,tableColorsButtons:["tableBack","|"],tableColors:["#61BD6D","#1ABC9C","#54ACD2","#2C82C9","#9365B8","#475577","#CCCCCC","#41A85F","#00A885","#3D8EB9","#2969B0","#553982","#28324E","#000000","#F7DA64","#FBA026","#EB6B56","#E25041","#A38F84","#EFEFEF","#FFFFFF","#FAC51C","#F37934","#D14841","#B8312F","#7C706B","#D1D5D8","REMOVE"],tableColorsStep:7,tableCellStyles:{"fr-highlighted":"Highlighted","fr-thick":"Thick"},tableStyles:{"fr-dashed-borders":"Dashed Borders","fr-alternate-rows":"Alternate Rows"},tableCellMultipleStyles:!0,tableMultipleStyles:!0,tableInsertHelper:!0,tableInsertHelperOffset:15}),ve.PLUGINS.table=function(R){var A,f,n,r,l,o,O,y=R.$;function h(){var e=x();if(e){var t=R.popups.get("table.edit");if(t||(t=p()),t){R.popups.setContainer("table.edit",R.$sc);var a=T(e),l=a.left+(a.right-a.left)/2,n=a.bottom;R.popups.show("table.edit",l,n,a.bottom-a.top,!0),R.edit.isDisabled()&&(1<Q().length&&R.toolbar.disable(),R.$el.removeClass("fr-no-selection"),R.edit.on(),R.button.bulkRefresh(),R.selection.setAtEnd(R.$el.find(".fr-selected-cell").last().get(0)),R.selection.restore())}}}function s(){var e=x();if(e){var t=R.popups.get("table.colors");t||(t=function o(){var e="";0<R.opts.tableColorsButtons.length&&(e='<div class="fr-buttons fr-tabs">'.concat(R.button.buildList(R.opts.tableColorsButtons),"</div>"));var t="";R.opts.colorsHEXInput&&(t='<div class="fr-color-hex-layer fr-table-colors-hex-layer fr-active fr-layer" id="fr-table-colors-hex-layer-'.concat(R.id,'"><div class="fr-input-line"><input maxlength="7" id="fr-table-colors-hex-layer-text-').concat(R.id,'" type="text" placeholder="').concat(R.language.translate("HEX Color"),'" tabIndex="1" aria-required="true"></div><div class="fr-action-buttons"><button type="button" class="fr-command fr-submit" data-cmd="tableCellBackgroundCustomColor" tabIndex="2" role="button">').concat(R.language.translate("OK"),"</button></div></div>"));var a={buttons:e,colors:function n(){for(var e='<div class="fr-color-set fr-table-colors">',t=0;t<R.opts.tableColors.length;t++)0!==t&&t%R.opts.tableColorsStep==0&&(e+="<br>"),"REMOVE"!=R.opts.tableColors[t]?e+='<span class="fr-command" style="background: '.concat(R.opts.tableColors[t],';" tabIndex="-1" role="button" data-cmd="tableCellBackgroundColor" data-param1="').concat(R.opts.tableColors[t],'"><span class="fr-sr-only">').concat(R.language.translate("Color")," ").concat(R.opts.tableColors[t],"&nbsp;&nbsp;&nbsp;</span></span>"):e+='<span class="fr-command" data-cmd="tableCellBackgroundColor" tabIndex="-1" role="button" data-param1="REMOVE" title="'.concat(R.language.translate("Clear Formatting"),'">').concat(R.icon.create("tableColorRemove"),'<span class="fr-sr-only">').concat(R.language.translate("Clear Formatting"),"</span></span>");return e+="</div>"}(),custom_color:t},l=R.popups.create("table.colors",a);return R.events.$on(R.$wp,"scroll.table-colors",function(){R.popups.isVisible("table.colors")&&s()}),function r(u){R.events.on("popup.tab",function(e){var t=y(e.currentTarget);if(!R.popups.isVisible("table.colors")||!t.is("span"))return!0;var a=e.which,l=!0;if(ve.KEYCODE.TAB==a){var n=u.find(".fr-buttons");l=!R.accessibility.focusToolbar(n,!!e.shiftKey)}else if(ve.KEYCODE.ARROW_UP==a||ve.KEYCODE.ARROW_DOWN==a||ve.KEYCODE.ARROW_LEFT==a||ve.KEYCODE.ARROW_RIGHT==a){var r=t.parent().find("span.fr-command"),o=r.index(t),s=R.opts.colorsStep,i=Math.floor(r.length/s),f=o%s,c=Math.floor(o/s),d=c*s+f,p=i*s;ve.KEYCODE.ARROW_UP==a?d=((d-s)%p+p)%p:ve.KEYCODE.ARROW_DOWN==a?d=(d+s)%p:ve.KEYCODE.ARROW_LEFT==a?d=((d-1)%p+p)%p:ve.KEYCODE.ARROW_RIGHT==a&&(d=(d+1)%p);var h=y(r.get(d));R.events.disableBlur(),h.focus(),l=!1}else ve.KEYCODE.ENTER==a&&(R.button.exec(t),l=!1);return!1===l&&(e.preventDefault(),e.stopPropagation()),l},!0)}(l),l}()),R.popups.setContainer("table.colors",R.$sc);var a=T(e),l=(a.left+a.right)/2,n=a.bottom;!function r(){var e=R.popups.get("table.colors"),t=R.$el.find(".fr-selected-cell").first(),a=R.helpers.RGBToHex(t.css("background-color")),l=e.find(".fr-table-colors-hex-layer input");e.find(".fr-selected-color").removeClass("fr-selected-color fr-active-item"),e.find('span[data-param1="'.concat(a,'"]')).addClass("fr-selected-color fr-active-item"),l.val(a).trigger("change")}(),R.popups.show("table.colors",l,n,a.bottom-a.top,!0)}}function i(){0===Q().length&&R.toolbar.enable()}function c(e){if(e)return R.popups.onHide("table.insert",function(){R.popups.get("table.insert").find('.fr-table-size .fr-select-table-size > span[data-row="1"][data-col="1"]').trigger("mouseover")}),!0;var t="";0<R.opts.tableInsertButtons.length&&(t='<div class="fr-buttons fr-tabs">'.concat(R.button.buildList(R.opts.tableInsertButtons),"</div>"));var a={buttons:t,rows_columns:function r(){for(var e='<div class="fr-table-size"><div class="fr-table-size-info">1 &times; 1</div><div class="fr-select-table-size">',t=1;t<=R.opts.tableInsertMaxSize;t++){for(var a=1;a<=R.opts.tableInsertMaxSize;a++){var l="inline-block";2<t&&!R.helpers.isMobile()&&(l="none");var n="fr-table-cell ";1==t&&1==a&&(n+=" hover"),e+='<span class="fr-command '.concat(n,'" tabIndex="-1" data-cmd="tableInsert" data-row="').concat(t,'" data-col="').concat(a,'" data-param1="').concat(t,'" data-param2="').concat(a,'" style="display: ').concat(l,';" role="button"><span></span><span class="fr-sr-only">').concat(t," &times; ").concat(a,"&nbsp;&nbsp;&nbsp;</span></span>")}e+='<div class="new-line"></div>'}return e+="</div></div>"}()},l=R.popups.create("table.insert",a);return R.events.$on(l,"mouseover",".fr-table-size .fr-select-table-size .fr-table-cell",function(e){d(y(e.currentTarget))},!0),function n(e){R.events.$on(e,"focus","[tabIndex]",function(e){var t=y(e.currentTarget);d(t)}),R.events.on("popup.tab",function(e){var t=y(e.currentTarget);if(!R.popups.isVisible("table.insert")||!t.is("span, a"))return!0;var a,l=e.which;if(ve.KEYCODE.ARROW_UP==l||ve.KEYCODE.ARROW_DOWN==l||ve.KEYCODE.ARROW_LEFT==l||ve.KEYCODE.ARROW_RIGHT==l){if(t.is("span.fr-table-cell")){var n=t.parent().find("span.fr-table-cell"),r=n.index(t),o=R.opts.tableInsertMaxSize,s=r%o,i=Math.floor(r/o);ve.KEYCODE.ARROW_UP==l?i=Math.max(0,i-1):ve.KEYCODE.ARROW_DOWN==l?i=Math.min(R.opts.tableInsertMaxSize-1,i+1):ve.KEYCODE.ARROW_LEFT==l?s=Math.max(0,s-1):ve.KEYCODE.ARROW_RIGHT==l&&(s=Math.min(R.opts.tableInsertMaxSize-1,s+1));var f=i*o+s,c=y(n.get(f));d(c),R.events.disableBlur(),c.focus(),a=!1}}else ve.KEYCODE.ENTER==l&&(R.button.exec(t),a=!1);return!1===a&&(e.preventDefault(),e.stopPropagation()),a},!0)}(l),l}function d(e){var t=e.data("row");null!==t&&(t=parseInt(t));var a=e.data("col");null!==a&&(a=parseInt(a));var l=e.parent();l.siblings(".fr-table-size-info").html("".concat(t," &times; ").concat(a)),l.find("> span").removeClass("hover fr-active-item");for(var n=1;n<=R.opts.tableInsertMaxSize;n++)for(var r=0;r<=R.opts.tableInsertMaxSize;r++){var o=l.find('> span[data-row="'.concat(n,'"][data-col="').concat(r,'"]'));n<=t&&r<=a?o.addClass("hover"):n<=t+1||n<=2&&!R.helpers.isMobile()?o.css("display","inline-block"):2<n&&!R.helpers.isMobile()&&o.css("display","none")}e.addClass("fr-active-item")}function p(e){if(e)return R.popups.onHide("table.edit",i),!0;if(0<R.opts.tableEditButtons.length){var t={buttons:'<div class="fr-buttons">'.concat(R.button.buildList(R.opts.tableEditButtons),"</div>")},a=R.popups.create("table.edit",t);return R.events.$on(R.$wp,"scroll.table-edit",function(){R.popups.isVisible("table.edit")&&h()}),a}return!1}function u(){if(0<Q().length){var e=ee();R.selection.setBefore(e.get(0))||R.selection.setAfter(e.get(0)),R.selection.restore(),R.popups.hide("table.edit"),R.opts.trackChangesEnabled?(R.track_changes.removedTable(e),D()):e.remove(),R.toolbar.enable()}}function b(e){var t=ee();if(0<t.length){if(0<R.$el.find("th.fr-selected-cell").length&&"above"==e)return;var a,l,n,r=x(),o=S(r);if(null==o)return;l="above"==e?o.min_i:o.max_i;var s="<tr>";for(a=0;a<r[l].length;a++){if("below"==e&&l<r.length-1&&r[l][a]==r[l+1][a]||"above"==e&&0<l&&r[l][a]==r[l-1][a]){if(0===a||0<a&&r[l][a]!=r[l][a-1]){var i=y(r[l][a]);i.attr("rowspan",parseInt(i.attr("rowspan"),10)+1)}}else s+='<td style="'+y(r[l][a]).attr("style")+'" ><br></td>'}s+="</tr>",n=0<R.$el.find("th.fr-selected-cell").length&&"below"==e?y(t.find("tbody").not(t.find("> table tbody"))):y(t.find("tr").not(t.find("> table tr")).get(l)),"below"==e?"TBODY"==n.attr("tagName")?n.prepend(s):n[0].parentNode&&n[0].insertAdjacentHTML("afterend",s):"above"==e&&(n.before(s),R.popups.isVisible("table.edit")&&h())}}function g(e,t,a){var l,n,r,o,s,i=0,f=x(a);if(e<(t=Math.min(t,f[0].length-1)))for(n=e;n<=t;n++)if(!(e<n&&f[0][n]==f[0][n-1])&&1<(o=Math.min(parseInt(f[0][n].getAttribute("colspan"),10)||1,t-e+1))&&f[0][n]==f[0][n+1])for(i=o-1,l=1;l<f.length;l++)if(f[l][n]!=f[l-1][n]){for(r=n;r<n+o;r++)if(f[l][r]!==undefined)if(1<(s=parseInt(f[l][r].getAttribute("colspan"),10)||1)&&f[l][r]==f[l][r+1])r+=i=Math.min(i,s-1);else if(!(i=Math.max(0,i-1)))break;if(!i)break}i&&v(f,i,"colspan",0,f.length-1,e,t)}function m(e,t,a){var l,n,r,o,s,i=0,f=x(a);if(e<(t=Math.min(t,f.length-1)))for(l=e;l<=t;l++)if(!(e<l&&f[l][0]==f[l-1][0])&&f[l][0]!==undefined&&1<(o=Math.min(parseInt(f[l][0].getAttribute("rowspan"),10)||1,t-e+1))&&f[l][0]==f[l+1][0])for(i=o-1,n=1;n<f[0].length;n++)if(f[l][n]!=f[l][n-1]){for(r=l;r<l+o;r++)if(f[r][n]!==undefined)if(1<(s=parseInt(f[r][n].getAttribute("rowspan"),10)||1)&&f[r][n]==f[r+1][n])r+=i=Math.min(i,s-1);else if(!(i=Math.max(0,i-1)))break;if(!i)break}i&&v(f,i,"rowspan",e,t,0,f[0].length-1)}function v(e,t,a,l,n,r,o){var s,i,f;for(s=l;s<=n;s++)for(i=r;i<=o;i++)l<s&&e[s][i]==e[s-1][i]||r<i&&e[s][i]==e[s][i-1]||e[s][i]!==undefined&&1<(f=parseInt(e[s][i].getAttribute(a),10)||1)&&(1<f-t?e[s][i].setAttribute(a,f-t):e[s][i].removeAttribute(a))}function w(e,t,a,l,n){m(e,t,n),g(a,l,n)}function t(e){var t=R.$el.find(".fr-selected-cell");"REMOVE"!=e?t.css("background-color",R.helpers.HEXtoRGB(e)):t.css("background-color",""),h()}function C(e){e.style.removeProperty("border"),e.style.removeProperty("border-top"),e.style.removeProperty("border-bottom"),e.style.removeProperty("border-left"),e.style.removeProperty("border-right")}function x(e){var f=[];if(null==(e=e||null)&&0<Q().length&&(e=ee()),e){for(var t=e.find("tr:empty"),a=t.length-1;0<=a;a--)y(t[a]).remove();e.findVisible("tr").not(e.find("> table tr")).each(function(s,e){var t=y(e),i=0;t.find("> th, > td").each(function(e,t){for(var a=y(t),l=parseInt(a.attr("colspan"),10)||1,n=parseInt(a.attr("rowspan"),10)||1,r=s;r<s+n;r++)for(var o=i;o<i+l;o++)f[r]||(f[r]=[]),f[r][o]?i++:f[r][o]=t;i+=l})})}return f}function $(e,t){for(var a=0;a<t.length;a++)for(var l=0;l<t[a].length;l++)if(t[a][l]==e)return{row:a,col:l}}function _(e,t,a){for(var l=e+1,n=t+1;l<a.length;){if(a[l][t]!=a[e][t]){l--;break}l++}for(l==a.length&&l--;n<a[e].length;){if(a[e][n]!=a[e][t]){n--;break}n++}return n==a[e].length&&n--,{row:l,col:n}}function E(){R.el.querySelector(".fr-cell-fixed")&&R.el.querySelector(".fr-cell-fixed").classList.remove("fr-cell-fixed"),R.el.querySelector(".fr-cell-handler")&&R.el.querySelector(".fr-cell-handler").classList.remove("fr-cell-handler")}function D(){var e=R.$el.find(".fr-selected-cell");0<e.length&&e.each(function(){var e=y(this);e.removeClass("fr-selected-cell"),""===e.attr("class")&&e.removeAttr("class")}),E()}function M(){R.events.disableBlur(),R.selection.clear(),R.$el.addClass("fr-no-selection"),R.$el.blur(),R.events.enableBlur()}function S(e){var t=R.$el.find(".fr-selected-cell");if(0<t.length){var a,l=e.length,n=0,r=e[0].length,o=0;for(a=0;a<t.length;a++){var s=$(t[a],e),i=_(s.row,s.col,e);l=Math.min(s.row,l),n=Math.max(i.row,n),r=Math.min(s.col,r),o=Math.max(i.col,o)}return{min_i:l,max_i:n,min_j:r,max_j:o}}return null}function T(e){var t=S(e);if(null!=t){var a=y(e[t.min_i][t.min_j]),l=y(e[t.min_i][t.max_j]),n=y(e[t.max_i][t.min_j]);return{left:a.length&&a.offset().left,right:l.length&&l.offset().left+l.outerWidth(),top:a.length&&a.offset().top,bottom:n.length&&n.offset().top+n.outerHeight()}}}function I(e,t){if(y(e).is(t))D(),y(e).addClass("fr-selected-cell");else{M(),R.edit.off();var a=x(),l=$(e,a),n=$(t,a),r=function h(e,t,a,l,n){var r,o,s,i,f=e,c=t,d=a,p=l;for(r=f;r<=c;r++)(1<(parseInt(y(n[r][d]).attr("rowspan"),10)||1)||1<(parseInt(y(n[r][d]).attr("colspan"),10)||1))&&(i=_((s=$(n[r][d],n)).row,s.col,n),f=Math.min(s.row,f),c=Math.max(i.row,c),d=Math.min(s.col,d),p=Math.max(i.col,p)),(1<(parseInt(y(n[r][p]).attr("rowspan"),10)||1)||1<(parseInt(y(n[r][p]).attr("colspan"),10)||1))&&(i=_((s=$(n[r][p],n)).row,s.col,n),f=Math.min(s.row,f),c=Math.max(i.row,c),d=Math.min(s.col,d),p=Math.max(i.col,p));for(o=d;o<=p;o++)(1<(parseInt(y(n[f][o]).attr("rowspan"),10)||1)||1<(parseInt(y(n[f][o]).attr("colspan"),10)||1))&&(i=_((s=$(n[f][o],n)).row,s.col,n),f=Math.min(s.row,f),c=Math.max(i.row,c),d=Math.min(s.col,d),p=Math.max(i.col,p)),(1<(parseInt(y(n[c][o]).attr("rowspan"),10)||1)||1<(parseInt(y(n[c][o]).attr("colspan"),10)||1))&&(i=_((s=$(n[c][o],n)).row,s.col,n),f=Math.min(s.row,f),c=Math.max(i.row,c),d=Math.min(s.col,d),p=Math.max(i.col,p));return f==e&&c==t&&d==a&&p==l?{min_i:e,max_i:t,min_j:a,max_j:l}:h(f,c,d,p,n)}(Math.min(l.row,n.row),Math.max(l.row,n.row),Math.min(l.col,n.col),Math.max(l.col,n.col),a);D(),e.classList.add("fr-cell-fixed"),t.classList.add("fr-cell-handler");for(var o=r.min_i;o<=r.max_i;o++)for(var s=r.min_j;s<=r.max_j;s++)y(e).closest("table").is(y(a[o][s]).closest("table"))&&y(a[o][s]).addClass("fr-selected-cell")}}function N(e){var t=null,a=y(e.target);return"TD"==e.target.tagName||"TH"==e.target.tagName?t=e.target:0<a.closest("th",a.closest("thead")[0]).length?t=a.closest("th",a.closest("thead")[0]).get(0):0<a.closest("td",a.closest("tr")[0]).length&&(t=a.closest("td",a.closest("tr")[0]).get(0)),-1===R.$el.html.toString().search(t)?null:t}function K(){D(),R.popups.hide("table.edit")}function e(e){var t=N(e);if("false"==y(t).parents("[contenteditable]").not(".fr-element").not(".fr-img-caption").not("body").first().attr("contenteditable"))return!0;if(0<Q().length&&!t&&K(),!R.edit.isDisabled()||R.popups.isVisible("table.edit"))if(1!=e.which||1==e.which&&R.helpers.isMac()&&e.ctrlKey)(3==e.which||1==e.which&&R.helpers.isMac()&&e.ctrlKey)&&t&&K();else if(r=!0,t){0<Q().length&&!e.shiftKey&&K(),e.stopPropagation(),R.events.trigger("image.hideResizer"),R.events.trigger("video.hideResizer"),n=!0;var a=t.tagName.toLowerCase();e.shiftKey&&0<R.$el.find("".concat(a,".fr-selected-cell")).length?y(R.$el.find("".concat(a,".fr-selected-cell")).closest("table")).is(y(t).closest("table"))?I(l,t):M():((R.keys.ctrlKey(e)||e.shiftKey)&&"TD"===e.currentTarget.tagName&&(1<Q().length||0===y(t).find(R.selection.element()).length&&!y(t).is(R.selection.element()))&&M(),l=t,0<R.opts.tableEditButtons.length&&I(l,l))}}function a(e){if(!R.edit.isDisabled()&&R.popups.areVisible())return!0;var t=N(e);if(1===Q().length&&null===t&&(R.toolbar.enable(),D()),(1===Q().length&&t&&"TD"!==t.tagName&&"TH"!==t.tagName||!n&&!R.$tb.is(e.target)&&!R.$tb.is(y(e.target).closest(".fr-toolbar")))&&(R.toolbar.enable(),D()),("BODY"===e.target.tagName||"HTML"===e.target.tagName)&&!t&&0<Q().length&&R.toolbar.enable(),!(1!=e.which||1==e.which&&R.helpers.isMac()&&e.ctrlKey)){if(r=!1,n)n=!1,N(e)||1!=Q().length?0<Q().length?R.selection.isCollapsed()?(h(),R.toolbar.enable()):(D(),R.edit.on()):Q().length||(R.$el.removeClass("fr-no-selection"),R.edit.on()):D();if(O){O=!1,A.removeClass("fr-moving"),R.$el.removeClass("fr-no-selection"),R.edit.on();var a=parseFloat(A.css("left"))+R.opts.tableResizerOffset+R.$wp.offset().left;R.opts.iframe&&(a-=R.$iframe.offset().left),A.data("release-position",a),A.removeData("max-left"),A.removeData("max-right"),function C(){var e=A.data("origin"),t=A.data("release-position");if(e!==t){var a=A.data("first"),l=A.data("second"),n=A.data("table"),r=n.outerWidth();if(R.undo.canDo()||R.undo.saveStep(),null!=a&&null!=l){var o,s,i,f=x(n),c=[],d=[],p=[],h=[];for(o=0;o<f.length;o++)s=y(f[o][a]),i=y(f[o][l]),c[o]=s.outerWidth(),p[o]=i.outerWidth(),d[o]=c[o]/r*100,h[o]=p[o]/r*100;for(o=0;o<f.length;o++)if(s=y(f[o][a]),i=y(f[o][l]),f[o][a]!=f[o][l]){var u=(d[o]*(c[o]+t-e)/c[o]).toFixed(4);s.css("width",u+"%"),i.css("width",(d[o]+h[o]-u).toFixed(4)+"%")}}else{var b,g=n.parent(),m=r/g.width()*100,v=(parseInt(n.css("margin-left"),10)||0)/g.width()*100,w=(parseInt(n.css("margin-right"),10)||0)/g.width()*100;"rtl"==R.opts.direction&&0===l||"rtl"!=R.opts.direction&&0!==l?(b=(r+t-e)/r*m,n.css("margin-right","calc(100% - ".concat(Math.round(b).toFixed(4),"% - ").concat(Math.round(v).toFixed(4),"%)"))):("rtl"==R.opts.direction&&0!==l||"rtl"!=R.opts.direction&&0===l)&&(b=(r-t+e)/r*m,n.css("margin-left","calc(100% - ".concat(Math.round(b).toFixed(4),"% - ").concat(Math.round(w).toFixed(4),"%)"))),n.css("width","".concat(Math.round(b).toFixed(4),"%"))}R.selection.restore(),R.undo.saveStep(),R.events.trigger("table.resized",[n.get(0)])}A.removeData("origin"),A.removeData("release-position"),A.removeData("first"),A.removeData("second"),A.removeData("table")}(),W()}}}function k(e){if(!(y(e.currentTarget).is(y(e.originalEvent.relatedTarget))||e.currentTarget.contains(e.originalEvent.relatedTarget)||e.originalEvent.relatedTarget&&e.originalEvent.relatedTarget.contains(e.currentTarget))&&(R.events.$on(y("input"),"click",te),!0===n&&0<R.opts.tableEditButtons.length)){if(y(e.currentTarget).closest("table").is(ee())){if("TD"==e.currentTarget.tagName&&0===R.$el.find("th.fr-selected-cell").length)return void I(l,e.currentTarget);if("TH"==e.currentTarget.tagName&&0===R.$el.find("td.fr-selected-cell").length)return void I(l,e.currentTarget)}M()}}function B(e,t,a,l){for(var n,r=t;r!=R.el&&"TD"!=r.tagName&&"TH"!=r.tagName&&("up"==l?n=r.previousElementSibling:"down"==l&&(n=r.nextElementSibling),!n);)r=r.parentNode;"TD"==r.tagName||"TH"==r.tagName?function o(e,t){for(var a=e;a&&"TABLE"!=a.tagName&&a.parentNode!=R.el;)a=a.parentNode;if(a&&"TABLE"==a.tagName){var l=x(y(a));"up"==t?z($(e,l),a,l):"down"==t&&Y($(e,l),a,l)}}(r,l):n&&("up"==l&&R.selection.setAtEnd(n),"down"==l&&R.selection.setAtStart(n))}function z(e,t,a){0<y(".tribute-container").length&&"none"!=y(".tribute-container").css("display")||(0<e.row?R.selection.setAtEnd(a[e.row-1][e.col]):B(0,t,0,"up"))}function Y(e,t,a){if(!(0<y(".tribute-container").length&&"none"!=y(".tribute-container").css("display"))){var l=parseInt(a[e.row][e.col].getAttribute("rowspan"),10)||1;e.row<a.length-l?R.selection.setAtStart(a[e.row+l][e.col]):B(0,t,0,"down")}}function W(){A&&(A.find("div").css("opacity",0),A.css("top",0),A.css("left",0),A.css("height",0),A.find("div").css("height",0),A.hide())}function L(){f&&f.removeClass("fr-visible").css("left","-9999px")}function H(e,t){var a=y(t),l=a.closest("table"),n=l.parent();if(t&&"TD"!=t.tagName&&"TH"!=t.tagName&&(0<a.closest("td").length?t=a.closest("td"):0<a.closest("th").length&&(t=a.closest("th"))),!t||"TD"!=t.tagName&&"TH"!=t.tagName)A&&a.get(0)!=A.get(0)&&a.parent().get(0)!=A.get(0)&&R.core.sameInstance(A)&&W();else{if(a=y(t),0===R.$el.find(a).length)return!1;var r=a.offset().left-1,o=r+a.outerWidth();if(Math.abs(e.pageX-r)<=R.opts.tableResizerOffset||Math.abs(o-e.pageX)<=R.opts.tableResizerOffset){var s,i,f,c,d,p=x(l),h=$(t,p),u=_(h.row,h.col,p),b=l.offset().top,g=l.outerHeight()-1;"rtl"!=R.opts.direction?e.pageX-r<=R.opts.tableResizerOffset?(f=r,0<h.col?(c=r-X(h.col-1,p)+R.opts.tableResizingLimit,d=r+X(h.col,p)-R.opts.tableResizingLimit,s=h.col-1,i=h.col):(s=null,i=0,c=l.offset().left-1-parseInt(l.css("margin-left"),10),d=l.offset().left-1+l.width()-p[0].length*R.opts.tableResizingLimit)):o-e.pageX<=R.opts.tableResizerOffset&&(f=o,u.col<p[u.row].length&&p[u.row][u.col+1]?(c=o-X(u.col,p)+R.opts.tableResizingLimit,d=o+X(u.col+1,p)-R.opts.tableResizingLimit,s=u.col,i=u.col+1):(s=u.col,i=null,c=l.offset().left-1+p[0].length*R.opts.tableResizingLimit,d=n.offset().left-1+n.width()+parseFloat(n.css("padding-left")))):o-e.pageX<=R.opts.tableResizerOffset?(f=o,0<h.col?(c=o-X(h.col,p)+R.opts.tableResizingLimit,d=o+X(h.col-1,p)-R.opts.tableResizingLimit,s=h.col,i=h.col-1):(s=null,i=0,c=l.offset().left+p[0].length*R.opts.tableResizingLimit,d=n.offset().left-1+n.width()+parseFloat(n.css("padding-left")))):e.pageX-r<=R.opts.tableResizerOffset&&(f=r,u.col<p[u.row].length&&p[u.row][u.col+1]?(c=r-X(u.col+1,p)+R.opts.tableResizingLimit,d=r+X(u.col,p)-R.opts.tableResizingLimit,s=u.col+1,i=u.col):(s=u.col,i=null,c=n.offset().left+parseFloat(n.css("padding-left")),d=l.offset().left-1+l.width()-p[0].length*R.opts.tableResizingLimit)),A||function E(){R.shared.$table_resizer||(R.shared.$table_resizer=y(document.createElement("div")).attr("class","fr-table-resizer").html("<div></div>")),A=R.shared.$table_resizer,R.events.$on(A,"mousedown",function(e){return!R.core.sameInstance(A)||(0<Q().length&&K(),1==e.which?(R.selection.save(),O=!0,A.addClass("fr-moving"),M(),R.edit.off(),A.find("div").css("opacity",1),!1):void 0)}),R.events.$on(A,"mousemove",function(e){if(!R.core.sameInstance(A))return!0;O&&(R.opts.iframe&&(e.pageX-=R.$iframe.offset().left),U(e))}),R.events.on("shared.destroy",function(){A.html("").removeData().remove(),A=null},!0),R.events.on("destroy",function(){R.$el.find(".fr-selected-cell").removeClass("fr-selected-cell"),y("body").first().append(A.hide())},!0)}(),A.data("table",l),A.data("first",s),A.data("second",i),A.data("instance",R),R.$wp.append(A);var m=f-R.win.pageXOffset-R.opts.tableResizerOffset-R.$wp.offset().left,v=b-R.$wp.offset().top+R.$wp.scrollTop();if(R.opts.iframe){var w=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-top")),C=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-left"));m+=R.$iframe.offset().left+C,v+=R.$iframe.offset().top+w,c+=R.$iframe.offset().left,d+=R.$iframe.offset().left}A.data("max-left",c),A.data("max-right",d),A.data("origin",f-R.win.pageXOffset),A.css("top",v),A.css("left",m),A.css("height",g),A.find("div").css("height",g),A.css("padding-left",R.opts.tableResizerOffset),A.css("padding-right",R.opts.tableResizerOffset),A.show()}else R.core.sameInstance(A)&&W()}}function F(e,t){if(R.$box.find(".fr-line-breaker").isVisible())return!1;f||J(),R.$box.append(f),f.data("instance",R);var a,l=y(t).find("tr").first(),n=e.pageX,r=0,o=0;if(R.opts.iframe){var s=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-top")),i=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-left"));r+=R.$iframe.offset().left-R.helpers.scrollLeft()+i,o+=R.$iframe.offset().top-R.helpers.scrollTop()+s}l.find("th, td").each(function(){var e=y(this);return e.offset().left<=n&&n<e.offset().left+e.outerWidth()/2?(a=parseInt(f.find("a").css("width"),10),f.css("top",o+e.offset().top-R.$box.offset().top-a-5),f.css("left",r+e.offset().left-R.$box.offset().left-a/2),f.data("selected-cell",e),f.data("position","before"),f.addClass("fr-visible"),!1):e.offset().left+e.outerWidth()/2<=n&&n<e.offset().left+e.outerWidth()?(a=parseInt(f.find("a").css("width"),10),f.css("top",o+e.offset().top-R.$box.offset().top-a-5),f.css("left",r+e.offset().left-R.$box.offset().left+e.outerWidth()-a/2),f.data("selected-cell",e),f.data("position","after"),f.addClass("fr-visible"),!1):void 0})}function P(e,t){if(R.$box.find(".fr-line-breaker").isVisible())return!1;f||J(),R.$box.append(f),f.data("instance",R);var a,l=y(t),n=e.pageY,r=0,o=0;if(R.opts.iframe){var s=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-top")),i=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-left"));r+=R.$iframe.offset().left-R.helpers.scrollLeft()+i,o+=R.$iframe.offset().top-R.helpers.scrollTop()+s}l.find("tr").each(function(){var e=y(this);a=parseInt(f.find("a").css("width"),10);var t=r+e.offset().left-R.$box.offset().left;return t=0!==R.$box.offset().left?t-a-5:t+a-5,e.offset().top<=n&&n<e.offset().top+e.outerHeight()/2?(f.css("top",o+e.offset().top-R.$box.offset().top-a/2),f.css("left",t),f.data("selected-cell",e.find("td").first()),f.data("position","above"),f.addClass("fr-visible"),!1):e.offset().top+e.outerHeight()/2<=n&&n<e.offset().top+e.outerHeight()?(f.css("top",o+e.offset().top-R.$box.offset().top+e.outerHeight()-a/2),f.css("left",t),f.data("selected-cell",e.find("td").first()),f.data("position","below"),f.addClass("fr-visible"),!1):void 0})}function V(e){o=null;var t=R.doc.elementFromPoint(e.pageX-R.win.pageXOffset,e.pageY-R.win.pageYOffset);R.opts.tableResizer&&(!R.popups.areVisible()||R.popups.areVisible()&&R.popups.isVisible("table.edit"))&&H(e,t),!R.opts.tableInsertHelper||R.popups.areVisible()||R.$tb.hasClass("fr-inline")&&R.$tb.isVisible()||function r(e,t){if(0===Q().length){var a,l,n;if(t&&("HTML"==t.tagName||"BODY"==t.tagName||R.node.isElement(t)))for(a=1;a<=R.opts.tableInsertHelperOffset;a++){if(l=R.doc.elementFromPoint(e.pageX-R.win.pageXOffset,e.pageY-R.win.pageYOffset+a),y(l).hasClass("fr-tooltip"))return!0;if(l&&("TH"==l.tagName||"TD"==l.tagName||"TABLE"==l.tagName)&&(y(l).parents(".fr-wrapper").length||R.opts.iframe)&&"false"!=y(l).closest("table").attr("contenteditable"))return F(e,y(l).closest("table")),!0;if(n=R.doc.elementFromPoint(e.pageX-R.win.pageXOffset+a,e.pageY-R.win.pageYOffset),y(n).hasClass("fr-tooltip"))return!0;if(n&&("TH"==n.tagName||"TD"==n.tagName||"TABLE"==n.tagName)&&(y(n).parents(".fr-wrapper").length||R.opts.iframe)&&"false"!=y(n).closest("table").attr("contenteditable"))return P(e,y(n).closest("table")),!0}R.core.sameInstance(f)&&L()}}(e,t)}function j(){if(O){var e=A.data("table").offset().top-R.win.pageYOffset;if(R.opts.iframe){var t=R.helpers.getPX(R.$wp.find(".fr-iframe").css("padding-top"));e+=R.$iframe.offset().top-R.helpers.scrollTop()+t}A.css("top",e)}}function X(e,t){var a,l=y(t[0][e]).outerWidth();for(a=1;a<t.length;a++)l=Math.min(l,y(t[a][e]).outerWidth());return l}function G(e,t,a){var l,n=0;for(l=e;l<=t;l++)n+=X(l,a);return n}function U(e){if(1<Q().length&&r&&M(),!1===r&&!1===n&&!1===O)o&&clearTimeout(o),R.edit.isDisabled()&&!R.popups.isVisible("table.edit")||(o=setTimeout(V,30,e));else if(O){var t=e.pageX-R.win.pageXOffset;R.opts.iframe&&(t+=R.$iframe.offset().left);var a=A.data("max-left"),l=A.data("max-right");a<=t&&t<=l?A.css("left",t-R.opts.tableResizerOffset-R.$wp.offset().left):t<a&&parseFloat(A.css("left"),10)>a-R.opts.tableResizerOffset?A.css("left",a-R.opts.tableResizerOffset-R.$wp.offset().left):l<t&&parseFloat(A.css("left"),10)<l-R.opts.tableResizerOffset&&A.css("left",l-R.opts.tableResizerOffset-R.$wp.offset().left)}else r&&L()}function q(e){R.node.isEmpty(e.get(0))?e.prepend(ve.MARKERS):e.prepend(ve.START_MARKER).append(ve.END_MARKER)}function J(){R.shared.$ti_helper||(R.shared.$ti_helper=y(document.createElement("div")).attr("class","fr-insert-helper").html('<a class="fr-floating-btn" role="button" tabIndex="-1" title="'.concat(R.language.translate("Insert"),'"><svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg"><path d="M22,16.75 L16.75,16.75 L16.75,22 L15.25,22.000 L15.25,16.75 L10,16.75 L10,15.25 L15.25,15.25 L15.25,10 L16.75,10 L16.75,15.25 L22,15.25 L22,16.75 Z"/></svg></a>')),R.events.bindClick(R.shared.$ti_helper,"a",function(){var e=f.data("selected-cell"),t=f.data("position"),a=f.data("instance")||R;"before"==t?(R.undo.saveStep(),e.addClass("fr-selected-cell"),a.table.insertColumn(t),e.removeClass("fr-selected-cell"),R.undo.saveStep()):"after"==t?(R.undo.saveStep(),e.addClass("fr-selected-cell"),a.table.insertColumn(t),e.removeClass("fr-selected-cell"),R.undo.saveStep()):"above"==t?(R.undo.saveStep(),e.addClass("fr-selected-cell"),a.table.insertRow(t),e.removeClass("fr-selected-cell"),R.undo.saveStep()):"below"==t&&(R.undo.saveStep(),e.addClass("fr-selected-cell"),a.table.insertRow(t),e.removeClass("fr-selected-cell"),R.undo.saveStep()),L()}),R.events.on("shared.destroy",function(){R.shared.$ti_helper.html("").removeData().remove(),R.shared.$ti_helper=null},!0),R.events.$on(R.shared.$ti_helper,"mousemove",function(e){e.stopPropagation()},!0),R.events.$on(y(R.o_win),"scroll",function(){L()},!0),R.events.$on(R.$wp,"scroll",function(){L()},!0)),f=R.shared.$ti_helper,R.events.on("destroy",function(){f=null}),R.tooltip.bind(R.$box,".fr-insert-helper > a.fr-floating-btn")}function Z(){l=null,clearTimeout(o)}function Q(){return R.el.querySelectorAll(".fr-selected-cell")}function ee(){var e=Q();if(e.length){for(var t=e[0];t&&"TABLE"!=t.tagName&&t.parentNode!=R.el;)t=t.parentNode;return t&&"TABLE"==t.tagName?y(t):y([])}return y([])}function te(e){n=!1}return{_init:function ae(){if(!R.$wp)return!1;if(R.helpers.isMobile()&&(R.events.$on(R.$el,"mousedown",e),R.events.$on(R.$win,"mouseup",a)),!R.helpers.isMobile()){O=n=r=!1,R.events.$on(R.$el,"mousedown",e),R.popups.onShow("image.edit",function(){D(),n=r=!1}),R.popups.onShow("link.edit",function(){D(),n=r=!1}),R.events.on("commands.mousedown",function(e){0<e.parents(".fr-toolbar").length&&D()}),R.events.$on(R.$el,"mouseover","th, td",k),R.events.$on(R.$win,"mouseup",a),R.opts.iframe&&R.events.$on(y(R.o_win),"mouseup",a),R.events.$on(R.$win,"mousemove",U),R.events.$on(y(R.o_win),"scroll",j),R.events.on("contentChanged",function(){0<Q().length&&(h(),R.$el.find("img").on("load.selected-cells",function(){y(this).off("load.selected-cells"),0<Q().length&&h()}))}),R.events.$on(y(R.o_win),"resize",function(){D()}),R.events.on("toolbar.esc",function(){if(0<Q().length)return R.events.disableBlur(),R.events.focus(),!1},!0),R.events.$on(y(R.o_win),"keydown",function(){r&&n&&(n=r=!1,R.$el.removeClass("fr-no-selection"),R.edit.on(),R.selection.setAtEnd(R.$el.find(".fr-selected-cell").last().get(0)),R.selection.restore(),D())}),R.events.$on(R.$el,"keydown",function(e){e.shiftKey?!1===function o(e){var t=Q();if(null!=t&&0<t.length){var a,l=x(),n=e.which,r=$(1==t.length?a=t[0]:(a=R.el.querySelector(".fr-cell-fixed"),R.el.querySelector(".fr-cell-handler")),l);if(ve.KEYCODE.ARROW_RIGHT==n){if(r.col<l[0].length-1)return I(a,l[r.row][r.col+1]),!1}else if(ve.KEYCODE.ARROW_DOWN==n){if(r.row<l.length-1)return I(a,l[r.row+1][r.col]),!1}else if(ve.KEYCODE.ARROW_LEFT==n){if(0<r.col)return I(a,l[r.row][r.col-1]),!1}else if(ve.KEYCODE.ARROW_UP==n&&0<r.row)return I(a,l[r.row-1][r.col]),!1}}(e)&&setTimeout(function(){h()},0):function s(e){var t=e.which,a=R.selection.blocks();if(a.length&&("TD"==(a=a[0]).tagName||"TH"==a.tagName)){for(var l=a;l&&"TABLE"!=l.tagName&&l.parentNode!=R.el;)l=l.parentNode;if(l&&"TABLE"==l.tagName&&(ve.KEYCODE.ARROW_LEFT==t||ve.KEYCODE.ARROW_UP==t||ve.KEYCODE.ARROW_RIGHT==t||ve.KEYCODE.ARROW_DOWN==t)&&(0<Q().length&&K(),R.browser.webkit&&(ve.KEYCODE.ARROW_UP==t||ve.KEYCODE.ARROW_DOWN==t))){var n=R.selection.ranges(0).startContainer;if(n.nodeType==Node.TEXT_NODE&&(ve.KEYCODE.ARROW_UP==t&&(n.previousSibling&&"BR"!==n.previousSibling.tagName||n.previousSibling&&"BR"===n.previousSibling.tagName&&n.previousSibling.previousSibling)||ve.KEYCODE.ARROW_DOWN==t&&(n.nextSibling&&"BR"!==n.nextSibling.tagName||n.nextSibling&&"BR"===n.nextSibling.tagName&&n.nextSibling.nextSibling)))return;e.preventDefault(),e.stopPropagation();var r=x(y(l)),o=$(a,r);return ve.KEYCODE.ARROW_UP==t?z(o,l,r):ve.KEYCODE.ARROW_DOWN==t&&Y(o,l,r),R.selection.restore(),!1}}}(e)}),R.events.on("keydown",function(e){if(!1===function l(e){if(e.which==ve.KEYCODE.TAB){var t;if(0<Q().length)t=R.$el.find(".fr-selected-cell").last();else{var a=R.selection.element();"TD"==a.tagName||"TH"==a.tagName?t=y(a):a!=R.el&&(0<y(a).parentsUntil(R.$el,"td").length?t=y(a).parents("td").first():0<y(a).parentsUntil(R.$el,"th").length&&(t=y(a).parents("th").first()))}if(t)return e.preventDefault(),!!(0===R.selection.get().focusOffset&&0<y(R.selection.element()).parentsUntil(R.$el,"ol, ul").length&&(0<y(R.selection.element()).closest("li").prev().length||y(R.selection.element()).is("li")&&0<y(R.selection.element()).prev().length))||(K(),e.shiftKey?0<t.prev().length?q(t.prev()):0<t.closest("tr").length&&0<t.closest("tr").prev().length?q(t.closest("tr").prev().find("td").last()):0<t.closest("tbody").length&&0<t.closest("table").find("thead tr").length&&q(t.closest("table").find("thead tr th").last()):0<t.next().length?q(t.next()):0<t.closest("tr").length&&0<t.closest("tr").next().length?q(t.closest("tr").next().find("td").first()):0<t.closest("thead").length&&0<t.closest("table").find("tbody tr").length?q(t.closest("table").find("tbody tr td").first()):(t.addClass("fr-selected-cell"),b("below"),D(),q(t.closest("tr").next().find("td").first())),R.selection.restore(),!1)}}(e))return!1;var t=Q();if(0<t.length){if(0<t.length&&R.keys.ctrlKey(e)&&e.which==ve.KEYCODE.A)return D(),R.popups.isVisible("table.edit")&&R.popups.hide("table.edit"),t=[],!0;if(e.which==ve.KEYCODE.ESC&&R.popups.isVisible("table.edit"))return D(),R.popups.hide("table.edit"),e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),!(t=[]);if(1<t.length&&(e.which==ve.KEYCODE.BACKSPACE||e.which==ve.KEYCODE.DELETE)){R.undo.saveStep();for(var a=0;a<t.length;a++)y(t[a]).html("<br>"),a==t.length-1&&y(t[a]).prepend(ve.MARKERS);return R.selection.restore(),R.undo.saveStep(),!(t=[])}if(1<t.length&&e.which!=ve.KEYCODE.F10&&!R.keys.isBrowserAction(e))return e.preventDefault(),!(t=[])}else if(!(t=[])===function n(e){if(e.altKey&&e.which==ve.KEYCODE.SPACE){var t,a=R.selection.element();if("TD"==a.tagName||"TH"==a.tagName?t=a:0<y(a).closest("td").length?t=y(a).closest("td").get(0):0<y(a).closest("th").length&&(t=y(a).closest("th").get(0)),t)return e.preventDefault(),I(t,t),h(),!1}}(e))return!1},!0);var t=[];R.events.on("html.beforeGet",function(){t=Q();for(var e=0;e<t.length;e++)t[e].className=(t[e].className||"").replace(/fr-selected-cell/g,"")}),R.events.on("html.afterGet",function(){for(var e=0;e<t.length;e++)t[e].className=(t[e].className?t[e].className.trim()+" ":"")+"fr-selected-cell";t=[]}),c(!0),p(!0)}R.events.on("destroy",Z)},insert:function le(e,t){var a,l,n="<table "+(R.opts.tableDefaultWidth?'style="width: '+R.opts.tableDefaultWidth+';" ':"")+'class="fr-inserted-table"><tbody>',r=100/t;for(a=0;a<e;a++){for(n+="<tr>",l=0;l<t;l++)n+="<td"+(R.opts.tableDefaultWidth?' style="width: '+r.toFixed(4)+'%;"':"")+">",0===a&&0===l&&(n+=ve.MARKERS),n+="<br></td>";n+="</tr>"}if(n+="</tbody></table>",R.opts.trackChangesEnabled){R.edit.on(),R.events.focus(!0),R.selection.restore(),R.undo.saveStep(),R.markers.insert(),R.html.wrap();var o=R.$el.find(".fr-marker");R.node.isLastSibling(o)&&o.parent().hasClass("fr-deletable")&&o.insertAfter(o.parent()),o.replaceWith(n),R.selection.clear()}else R.html.insert(n);R.selection.restore();var s=R.$el.find(".fr-inserted-table");s.removeClass("fr-inserted-table"),R.events.trigger("table.inserted",[s.get(0)])},remove:u,insertRow:b,deleteRow:function ne(){var e=ee();if(0<e.length){var t,a,l,n=x(),r=S(n);if(null==r)return;if(0===r.min_i&&r.max_i==n.length-1)u();else{for(t=r.max_i;t>=r.min_i;t--){for(l=y(e.find("tr").not(e.find("> table tr")).get(t)),a=0;a<n[t].length;a++)if(0===a||n[t][a]!=n[t][a-1]){var o=y(n[t][a]);if(1<parseInt(o.attr("rowspan"),10)){var s=parseInt(o.attr("rowspan"),10)-1;1==s?o.removeAttr("rowspan"):o.attr("rowspan",s)}if(t<n.length-1&&n[t][a]==n[t+1][a]&&(0===t||n[t][a]!=n[t-1][a])){for(var i=n[t][a],f=a;0<f&&n[t][f]==n[t][f-1];)f--;0===f?y(e.find("tr").not(e.find("> table tr")).get(t+1)).prepend(i):y(n[t+1][f-1])[0].parentNode&&y(n[t+1][f-1])[0].insertAdjacentElement("afterend",i)}}var c=l.parent();l.remove(),0===c.find("tr").length&&c.remove(),n=x(e)}w(0,n.length-1,0,n[0].length-1,e),0<r.min_i?R.selection.setAtEnd(n[r.min_i-1][0]):R.selection.setAtEnd(n[0][0]),R.selection.restore(),R.popups.hide("table.edit")}}},insertColumn:function re(i){var e=ee();if(0<e.length){var f,c=x(),t=S(c);f="before"==i?t.min_j:t.max_j;var a,d=100/c[0].length,p=100/(c[0].length+1);e.find("th, td").each(function(){(a=y(this)).data("old-width",a.outerWidth()/e.outerWidth()*100)}),e.find("tr").not(e.find("> table tr")).each(function(e){for(var t,a=y(this),l=0,n=0;l-1<f;){if(!(t=a.find("> th, > td").get(n))){t=null;break}t==c[e][l]?(l+=parseInt(y(t).attr("colspan"),10)||1,n++):(l+=parseInt(y(c[e][l]).attr("colspan"),10)||1,"after"==i&&(t=0===n?-1:a.find("> th, > td").get(n-1)))}var r,o=y(t);if("after"==i&&f<l-1||"before"==i&&0<f&&c[e][f]==c[e][f-1]){if(0===e||0<e&&c[e][f]!=c[e-1][f]){var s=parseInt(o.attr("colspan"),10)+1;o.attr("colspan",s),o.css("width",(o.data("old-width")*p/d+p).toFixed(4)+"%"),o.removeData("old-width")}}else r=0<a.find("th").length?'<th style="width: '.concat(p.toFixed(4),'%;"><br></th>'):'<td style="width: '.concat(p.toFixed(4),'%;"><br></td>'),-1==t?a.prepend(r):null==t?a.append(r):"before"==i?o.before(r):"after"==i&&o[0].parentNode&&o[0].insertAdjacentHTML("afterend",r)}),e.find("th, td").each(function(){(a=y(this)).data("old-width")&&(a.css("width",(a.data("old-width")*p/d).toFixed(4)+"%"),a.removeData("old-width"))}),R.popups.isVisible("table.edit")&&h()}},deleteColumn:function oe(){var e=ee();if(0<e.length){var t,a,l,n=x(),r=S(n);if(null==r)return;if(0===r.min_j&&r.max_j==n[0].length-1)u();else{var o=0;for(t=0;t<n.length;t++)for(a=0;a<n[0].length;a++)(l=y(n[t][a])).hasClass("fr-selected-cell")||(l.data("old-width",l.outerWidth()/e.outerWidth()*100),(a<r.min_j||a>r.max_j)&&(o+=l.outerWidth()/e.outerWidth()*100));for(o/=n.length,a=r.max_j;a>=r.min_j;a--)for(t=0;t<n.length;t++)if(0===t||n[t][a]!=n[t-1][a])if(l=y(n[t][a]),1<(parseInt(l.attr("colspan"),10)||1)){var s=parseInt(l.attr("colspan"),10)-1;1==s?l.removeAttr("colspan"):l.attr("colspan",s),l.css("width",(100*(l.data("old-width")-X(a,n))/o).toFixed(4)+"%"),l.removeData("old-width")}else{var i=y(l.parent().get(0));l.remove(),0===i.find("> th, > td").length&&(0===i.prev().length||0===i.next().length||i.prev().find("> th[rowspan], > td[rowspan]").length<i.prev().find("> th, > td").length)&&i.remove()}w(0,n.length-1,0,n[0].length-1,e),0<r.min_j?R.selection.setAtEnd(n[r.min_i][r.min_j-1]):R.selection.setAtEnd(n[r.min_i][0]),R.selection.restore(),R.popups.hide("table.edit"),e.find("th, td").each(function(){(l=y(this)).data("old-width")&&(l.css("width",(100*l.data("old-width")/o).toFixed(4)+"%"),l.removeData("old-width"))})}}},mergeCells:function se(){if(1<Q().length&&(0===R.$el.find("th.fr-selected-cell").length||0===R.$el.find("td.fr-selected-cell").length)){E();var e,t,a=S(x());if(null==a)return;var l=R.$el.find(".fr-selected-cell"),n=y(l[0]),r=n.parent().find(".fr-selected-cell"),o=n.closest("table"),s=n.html(),i=0;for(e=0;e<r.length;e++)i+=y(r[e]).outerWidth();for(n.css("width",Math.min(100,i/o.outerWidth()*100).toFixed(4)+"%"),a.min_j<a.max_j&&n.attr("colspan",a.max_j-a.min_j+1),a.min_i<a.max_i&&n.attr("rowspan",a.max_i-a.min_i+1),e=1;e<l.length;e++)"<br>"!=(t=y(l[e])).html()&&""!==t.html()&&(s+="<br>".concat(t.html())),t.remove();n.html(s),R.selection.setAtEnd(n.get(0)),R.selection.restore(),R.toolbar.enable(),m(a.min_i,a.max_i,o);var f=o.find("tr:empty");for(e=f.length-1;0<=e;e--)y(f[e]).remove();g(a.min_j,a.max_j,o),h()}},splitCellVertically:function ie(){if(1==Q().length){var e=R.$el.find(".fr-selected-cell"),t=parseInt(e.attr("colspan"),10)||1,a=e.parent().outerWidth(),l=e.outerWidth(),n=e.clone().html("<br>"),r=x(),o=$(e.get(0),r);if(1<t){var s=Math.ceil(t/2);l=G(o.col,o.col+s-1,r)/a*100;var i=G(o.col+s,o.col+t-1,r)/a*100;1<s?e.attr("colspan",s):e.removeAttr("colspan"),1<t-s?n.attr("colspan",t-s):n.removeAttr("colspan"),e.css("width",l.toFixed(4)+"%"),n.css("width",i.toFixed(4)+"%")}else{var f;for(f=0;f<r.length;f++)if(0===f||r[f][o.col]!=r[f-1][o.col]){var c=y(r[f][o.col]);if(!c.is(e)){var d=(parseInt(c.attr("colspan"),10)||1)+1;c.attr("colspan",d)}}l=l/a*100/2,e.css("width","".concat(l.toFixed(4),"%")),n.css("width","".concat(l.toFixed(4),"%"))}e[0].parentNode&&e[0].insertAdjacentElement("afterend",n[0]),D(),R.popups.hide("table.edit")}},splitCellHorizontally:function fe(){if(1==Q().length){var e=R.$el.find(".fr-selected-cell"),t=e.parent(),a=e.closest("table"),l=parseInt(e.attr("rowspan"),10),n=x(),r=$(e.get(0),n),o=e.clone().html("<br>");if(1<l){var s=Math.ceil(l/2);1<s?e.attr("rowspan",s):e.removeAttr("rowspan"),1<l-s?o.attr("rowspan",l-s):o.removeAttr("rowspan");for(var i=r.row+s,f=0===r.col?r.col:r.col-1;0<=f&&(n[i][f]==n[i][f-1]||0<i&&n[i][f]==n[i-1][f]);)f--;-1==f?y(a.find("tr").not(a.find("> table tr")).get(i)).prepend(o):y(n[i][f])[0].parentNode&&y(n[i][f])[0].insertAdjacentElement("afterend",o[0])}else{var c,d=y(document.createElement("tr")).append(o);for(c=0;c<n[0].length;c++)if(0===c||n[r.row][c]!=n[r.row][c-1]){var p=y(n[r.row][c]);p.is(e)||p.attr("rowspan",(parseInt(p.attr("rowspan"),10)||1)+1)}t[0].parentNode&&t[0].insertAdjacentElement("afterend",d[0])}D(),R.popups.hide("table.edit")}},addHeader:function ce(){var e=ee();if(0<e.length&&0===e.find("th").length){var t,a="<thead><tr>",l=0;for(e.find("tr").first().find("> td").each(function(){var e=y(this);l+=parseInt(e.attr("colspan"),10)||1}),t=0;t<l;t++)a+="<th><br></th>";a+="</tr></thead>",e.prepend(a),h()}},removeHeader:function de(){var e=ee(),t=e.find("thead");if(0<t.length)if(0===e.find("tbody tr").length)u();else if(t.remove(),0<Q().length)h();else{R.popups.hide("table.edit");var a=e.find("tbody tr").first().find("td").first().get(0);a&&(R.selection.setAtEnd(a),R.selection.restore())}},setBackground:t,showInsertPopup:function pe(){var e=R.$tb.find('.fr-command[data-cmd="insertTable"]'),t=R.popups.get("table.insert");if(t||(t=c()),!t.hasClass("fr-active")){R.popups.refresh("table.insert"),R.popups.setContainer("table.insert",R.$tb);var a=R.button.getPosition(e),l=a.left,n=a.top;R.popups.show("table.insert",l,n,e.outerHeight())}},showEditPopup:h,showColorsPopup:s,back:function he(){0<Q().length?h():(R.popups.hide("table.insert"),R.toolbar.showInline())},verticalAlign:function ue(e){R.$el.find(".fr-selected-cell").css("vertical-align",e)},horizontalAlign:function be(e){R.$el.find(".fr-selected-cell").css("text-align",e)},applyStyle:function ge(e,t,a,l){if(0<t.length){if(R.opts.useClasses||("TABLE"===t.get(0).tagName?function r(e,t){var a=e.childNodes;if(0<a.length)for(var l=0;l<a.length;l++)"TD"===a[l].tagName?"fr-dashed-borders"===t||"fr-highlighted"===t||"fr-thick"===t?C(a[l]):"fr-alternate-rows"===t&&a[l].style.removeProperty("background-color"):r(a[l],t)}(t.get(0),e):C(t.get(0))),!a){var n=Object.keys(l);n.splice(n.indexOf(e),1),t.removeClass(n.join(" "))}t.toggleClass(e)}},selectedTable:ee,selectedCells:Q,customColor:function me(){var e=R.popups.get("table.colors").find(".fr-table-colors-hex-layer input");e.length&&t(e.val())},selectCells:I}},ve.DefineIcon("insertTable",{NAME:"table",SVG_KEY:"insertTable"}),ve.RegisterCommand("insertTable",{title:"Insert Table",undo:!1,focus:!0,refreshOnCallback:!1,popup:!0,callback:function(){this.popups.isVisible("table.insert")?(this.$el.find(".fr-marker").length&&(this.events.disableBlur(),this.selection.restore()),this.popups.hide("table.insert")):this.table.showInsertPopup()},plugin:"table"}),ve.RegisterCommand("tableInsert",{callback:function(e,t,a){this.table.insert(t,a),this.popups.hide("table.insert")}}),ve.DefineIcon("tableHeader",{NAME:"header",FA5NAME:"heading",SVG_KEY:"tableHeader"}),ve.RegisterCommand("tableHeader",{title:"Table Header",focus:!1,toggle:!0,callback:function(){this.popups.get("table.edit").find('.fr-command[data-cmd="tableHeader"]').hasClass("fr-active")?this.table.removeHeader():this.table.addHeader()},refresh:function(e){var t=this.table.selectedTable();0<t.length&&(0===t.find("th").length?e.removeClass("fr-active").attr("aria-pressed",!1):e.addClass("fr-active").attr("aria-pressed",!0))}}),ve.DefineIcon("tableRows",{NAME:"bars",SVG_KEY:"row"}),ve.RegisterCommand("tableRows",{type:"dropdown",focus:!1,title:"Row",options:{above:"Insert row above",below:"Insert row below","delete":"Delete row"},html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=ve.COMMANDS.tableRows.options;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableRows" data-param1="'+a+'" title="'+this.language.translate(t[a])+'">'+this.language.translate(t[a])+"</a></li>");return e+="</ul>"},callback:function(e,t){"above"==t||"below"==t?this.table.insertRow(t):this.table.deleteRow()}}),ve.DefineIcon("tableColumns",{NAME:"bars fa-rotate-90",SVG_KEY:"columns"}),ve.RegisterCommand("tableColumns",{type:"dropdown",focus:!1,title:"Column",options:{before:"Insert column before",after:"Insert column after","delete":"Delete column"},html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=ve.COMMANDS.tableColumns.options;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableColumns" data-param1="'.concat(a,'" title="').concat(this.language.translate(t[a]),'">').concat(this.language.translate(t[a]),"</a></li>"));return e+="</ul>"},callback:function(e,t){"before"==t||"after"==t?this.table.insertColumn(t):this.table.deleteColumn()}}),ve.DefineIcon("tableCells",{NAME:"square-o",FA5NAME:"square",SVG_KEY:"cellOptions"}),ve.RegisterCommand("tableCells",{type:"dropdown",focus:!1,title:"Cell",options:{merge:"Merge cells","vertical-split":"Vertical split","horizontal-split":"Horizontal split"},html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=ve.COMMANDS.tableCells.options;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableCells" data-param1="'.concat(a,'" title="').concat(this.language.translate(t[a]),'">').concat(this.language.translate(t[a]),"</a></li>"));return e+="</ul>"},callback:function(e,t){"merge"==t?this.table.mergeCells():"vertical-split"==t?this.table.splitCellVertically():this.table.splitCellHorizontally()},refreshOnShow:function(e,t){1<this.$el.find(".fr-selected-cell").length?(t.find('a[data-param1="vertical-split"]').addClass("fr-disabled").attr("aria-disabled",!0),t.find('a[data-param1="horizontal-split"]').addClass("fr-disabled").attr("aria-disabled",!0),t.find('a[data-param1="merge"]').removeClass("fr-disabled").attr("aria-disabled",!1)):(t.find('a[data-param1="merge"]').addClass("fr-disabled").attr("aria-disabled",!0),t.find('a[data-param1="vertical-split"]').removeClass("fr-disabled").attr("aria-disabled",!1),t.find('a[data-param1="horizontal-split"]').removeClass("fr-disabled").attr("aria-disabled",!1))}}),ve.DefineIcon("tableRemove",{NAME:"trash",SVG_KEY:"removeTable"}),ve.RegisterCommand("tableRemove",{title:"Remove Table",focus:!1,callback:function(){this.table.remove()}}),ve.DefineIcon("tableStyle",{NAME:"paint-brush",SVG_KEY:"tableStyle"}),ve.RegisterCommand("tableStyle",{title:"Table Style",type:"dropdown",focus:!1,html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=this.opts.tableStyles;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableStyle" data-param1="'.concat(a,'" title="').concat(this.language.translate(t[a]),'">').concat(this.language.translate(t[a]),"</a></li>"));return e+="</ul>"},callback:function(e,t){this.table.applyStyle(t,this.$el.find(".fr-selected-cell").closest("table"),this.opts.tableMultipleStyles,this.opts.tableStyles)},refreshOnShow:function(e,t){var a=this.$,l=this.$el.find(".fr-selected-cell").closest("table");l&&t.find(".fr-command").each(function(){var e=a(this).data("param1"),t=l.hasClass(e);a(this).toggleClass("fr-active",t).attr("aria-selected",t)})}}),ve.DefineIcon("tableCellBackground",{NAME:"tint",SVG_KEY:"cellBackground"}),ve.RegisterCommand("tableCellBackground",{title:"Cell Background",focus:!1,popup:!0,callback:function(){this.table.showColorsPopup()}}),ve.RegisterCommand("tableCellBackgroundColor",{undo:!0,focus:!1,callback:function(e,t){this.table.setBackground(t)}}),ve.DefineIcon("tableBack",{NAME:"arrow-left",SVG_KEY:"back"}),ve.RegisterCommand("tableBack",{title:"Back",undo:!1,focus:!1,back:!0,callback:function(){this.table.back()},refresh:function(e){0!==this.table.selectedCells().length||this.opts.toolbarInline?(e.removeClass("fr-hidden"),e.next(".fr-separator").removeClass("fr-hidden")):(e.addClass("fr-hidden"),e.next(".fr-separator").addClass("fr-hidden"))}}),ve.DefineIcon("tableCellVerticalAlign",{NAME:"arrows-v",FA5NAME:"arrows-alt-v",SVG_KEY:"verticalAlignMiddle"}),ve.RegisterCommand("tableCellVerticalAlign",{type:"dropdown",focus:!1,title:"Vertical Align",options:{Top:"Align Top",Middle:"Align Middle",Bottom:"Align Bottom"},html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=ve.COMMANDS.tableCellVerticalAlign.options;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableCellVerticalAlign" data-param1="'.concat(a.toLowerCase(),'" title="').concat(this.language.translate(t[a]),'">').concat(this.language.translate(a),"</a></li>"));return e+="</ul>"},callback:function(e,t){this.table.verticalAlign(t)},refreshOnShow:function(e,t){t.find('.fr-command[data-param1="'+this.$el.find(".fr-selected-cell").css("vertical-align")+'"]').addClass("fr-active").attr("aria-selected",!0)}}),ve.DefineIcon("tableCellHorizontalAlign",{NAME:"align-left",SVG_KEY:"alignLeft"}),ve.DefineIcon("align-left",{NAME:"align-left",SVG_KEY:"alignLeft"}),ve.DefineIcon("align-right",{NAME:"align-right",SVG_KEY:"alignRight"}),ve.DefineIcon("align-center",{NAME:"align-center",SVG_KEY:"alignCenter"}),ve.DefineIcon("align-justify",{NAME:"align-justify",SVG_KEY:"alignJustify"}),ve.RegisterCommand("tableCellHorizontalAlign",{type:"dropdown",focus:!1,title:"Horizontal Align",options:{left:"Align Left",center:"Align Center",right:"Align Right",justify:"Align Justify"},html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=ve.COMMANDS.tableCellHorizontalAlign.options;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command fr-title" tabIndex="-1" role="option" data-cmd="tableCellHorizontalAlign" data-param1="'.concat(a,'" title="').concat(this.language.translate(t[a]),'">').concat(this.icon.create("align-".concat(a)),'<span class="fr-sr-only">').concat(this.language.translate(t[a]),"</span></a></li>"));return e+="</ul>"},callback:function(e,t){this.table.horizontalAlign(t)},refresh:function(e){var t=this.table.selectedCells(),a=this.$;t.length&&e.find("> *").first().replaceWith(this.icon.create("align-".concat(this.helpers.getAlignment(a(t[0])))))},refreshOnShow:function(e,t){t.find('.fr-command[data-param1="'+this.helpers.getAlignment(this.$el.find(".fr-selected-cell").first())+'"]').addClass("fr-active").attr("aria-selected",!0)}}),ve.DefineIcon("tableCellStyle",{NAME:"magic",SVG_KEY:"cellStyle"}),ve.RegisterCommand("tableCellStyle",{title:"Cell Style",type:"dropdown",focus:!1,html:function(){var e='<ul class="fr-dropdown-list" role="presentation">',t=this.opts.tableCellStyles;for(var a in t)t.hasOwnProperty(a)&&(e+='<li role="presentation"><a class="fr-command" tabIndex="-1" role="option" data-cmd="tableCellStyle" data-param1="'.concat(a,'" title="').concat(this.language.translate(t[a]),'">').concat(this.language.translate(t[a]),"</a></li>"));return e+="</ul>"},callback:function(e,t){this.table.applyStyle(t,this.$el.find(".fr-selected-cell"),this.opts.tableCellMultipleStyles,this.opts.tableCellStyles)},refreshOnShow:function(e,t){var a=this.$,l=this.$el.find(".fr-selected-cell").first();l&&t.find(".fr-command").each(function(){var e=a(this).data("param1"),t=l.hasClass(e);a(this).toggleClass("fr-active",t).attr("aria-selected",t)})}}),ve.RegisterCommand("tableCellBackgroundCustomColor",{title:"OK",undo:!0,callback:function(){this.table.customColor()}}),ve.DefineIcon("tableColorRemove",{NAME:"eraser",SVG_KEY:"remove"})});
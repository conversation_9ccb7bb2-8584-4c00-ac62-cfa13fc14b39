/*!
 * ScrollTrigger 3.3.3
 * https://greensock.com
 *
 * @license Copyright 2020, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for Club GreenSock members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */

!(function (e, t) {
  "object" == typeof exports && "undefined" != typeof module
    ? t(exports)
    : "function" == typeof define && define.amd
    ? define(["exports"], t)
    : t(((e = e || self).window = e.window || {}));
})(this, function (e) {
  "use strict";
  function z(e) {
    return e;
  }
  function A() {
    return "undefined" != typeof window;
  }
  function B() {
    return xe || (A() && (xe = window.gsap) && xe.registerPlugin && xe);
  }
  function C(e) {
    return !!~i.indexOf(e);
  }
  function D(t, e) {
    var r = e.s;
    return function (e) {
      return arguments.length ? (t[r] = e) : t[r];
    };
  }
  function E(e, t) {
    var r = t.s,
      n = t.d2;
    return (r = "scroll" + n) && C(e)
      ? Math.max(x[r], Se[r]) -
          (we["inner" + n] || x["client" + n] || Se["client" + n])
      : e[r] - e["offset" + n];
  }
  function F(e) {
    return "string" == typeof e;
  }
  function G(e) {
    return "function" == typeof e;
  }
  function H(e) {
    return "number" == typeof e;
  }
  function I(e) {
    return "object" == typeof e;
  }
  function ba(e) {
    return we.getComputedStyle(e);
  }
  function da(e, t) {
    for (var r in t) r in e || (e[r] = t[r]);
    return e;
  }
  function ea(e, t) {
    var r =
        t &&
        "matrix(1, 0, 0, 1, 0, 0)" !== ba(e)[f] &&
        xe
          .to(e, {
            x: 0,
            y: 0,
            xPercent: 0,
            yPercent: 0,
            rotation: 0,
            rotationX: 0,
            rotationY: 0,
            scale: 1,
            skewX: 0,
            skewY: 0,
          })
          .progress(1),
      n = e.getBoundingClientRect();
    return r && r.progress(0).kill(), n;
  }
  function fa(e, t) {
    var r = t.d2;
    return e["offset" + r] || e["client" + r] || 0;
  }
  function ha(t, r, e, n) {
    return e.split(",").forEach(function (e) {
      return t(r, e, n);
    });
  }
  function ia(e, t, r) {
    return e.addEventListener(t, r, { passive: !0 });
  }
  function ja(e, t, r) {
    return e.removeEventListener(t, r);
  }
  function na(e, t) {
    if (F(e)) {
      var r = e.indexOf("="),
        n = ~r ? (e.charAt(r - 1) + 1) * parseFloat(e.substr(r + 1)) : 0;
      n && (e.indexOf("%") > r && (n *= t / 100), (e = e.substr(0, r - 1))),
        (e =
          n +
          (e in g
            ? g[e] * t
            : ~e.indexOf("%")
            ? (parseFloat(e) * t) / 100
            : parseFloat(e) || 0));
    }
    return e;
  }
  function oa(e, t, r, n, o, i, a) {
    var s = o.startColor,
      l = o.endColor,
      c = o.fontSize,
      f = o.indent,
      u = o.fontWeight,
      p = Te.createElement("div"),
      d = C(r),
      g = -1 !== e.indexOf("scroller"),
      h = d ? Se : r,
      v = -1 !== e.indexOf("start"),
      m = v ? s : l,
      b =
        "border-color:" +
        m +
        ";font-size:" +
        c +
        ";color:" +
        m +
        ";font-weight:" +
        u +
        ";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";
    return (
      (b += "position:" + (g && d ? "fixed;" : "absolute;")),
      (!g && d) ||
        (b += (n === Xe ? y : w) + ":" + (i + parseFloat(f)) + "px;"),
      a &&
        (b +=
          "box-sizing:border-box;text-align:left;width:" +
          a.offsetWidth +
          "px;"),
      (p._isStart = v),
      p.setAttribute("class", "gsap-marker-" + e),
      (p.style.cssText = b),
      (p.innerText = t || 0 === t ? e + "-" + t : e),
      h.insertBefore(p, h.children[0]),
      (p._offset = p["offset" + n.op.d2]),
      T(p, 0, n, d, v),
      p
    );
  }
  function sa() {
    return (l = l || s(m));
  }
  function ta() {
    l || ((l = s(m)), ze || v("scrollStart"), (ze = Oe()));
  }
  function ua() {
    return !Me && 200 < Oe() - ze && a.restart(!0);
  }
  function ya(e) {
    for (var t = v("refreshInit"), r = Ve.length, n = r; n--; )
      Ve[n].scroll.rec = Ve[n].scroll();
    for (n = 0; n < r; n++) Ve[n] && Ve[n].refresh(!0 !== e);
    for (
      t.forEach(function (e) {
        return e && e.render && e.render(-1);
      }),
        n = Ve.length;
      n--;

    )
      Ve[n].scroll.rec = 0;
    v("refresh");
  }
  function Ca(e, t, r) {
    if (($e(r), e.parentNode === t)) {
      var n = t.parentNode;
      n && (n.insertBefore(e, t), n.removeChild(t));
    }
  }
  function Da(e, t, r) {
    if (e.parentNode !== t) {
      for (var n, o = b.length, i = t.style, a = e.style; o--; )
        i[(n = b[o])] = r[n];
      (i.position = "absolute" === r.position ? "absolute" : "relative"),
        (a[w] = a[y] = "auto"),
        (i.overflow = "visible"),
        (i.boxSizing = "border-box"),
        (i[Ie] = fa(e, Je) + qe),
        (i[Le] = fa(e, Xe) + qe),
        (i[Re] = a[je] = a[d] = a[p] = "0"),
        (a[Ie] = r[Ie]),
        (a[Le] = r[Le]),
        (a[Re] = r[Re]),
        e.parentNode.insertBefore(t, e),
        t.appendChild(e);
    }
  }
  function Ga(e) {
    for (var t = S.length, r = e.style, n = [], o = 0; o < t; o++)
      n.push(S[o], r[S[o]]);
    return (n.t = e), n;
  }
  function Ja(e, t, r, n, o, i, a, s, l, c, f, u) {
    if (
      (G(e) && (e = e(s)),
      F(e) &&
        "max" === e.substr(0, 3) &&
        (e = u + ("=" === e.charAt(4) ? na("0" + e.substr(3), r) : 0)),
      H(e))
    )
      a && T(a, r, n, f, !0);
    else {
      G(t) && (t = t(s));
      var p,
        d,
        g,
        h = Ce(t)[0] || Se,
        v = ea(h) || {},
        m = e.split(" ");
      (v && (v.left || v.top)) ||
        "none" !== ba(h).display ||
        ((g = h.style.display),
        (h.style.display = "block"),
        (v = ea(h)),
        g ? (h.style.display = g) : h.style.removeProperty("display")),
        (p = na(m[0], v[n.d])),
        (d = na(m[1] || "0", r)),
        (e = v[n.p] - l[n.p] - c + p + o - d),
        a && T(a, d, n, f, r - d < 20 || (a._isStart && 20 < d)),
        (r -= r - d);
    }
    if (i) {
      var b = e + r,
        y = i._isStart;
      (u = "scroll" + n.d2),
        T(
          i,
          b,
          n,
          f,
          (y && 20 < b) ||
            (!y && (f ? Math.max(Se[u], x[u]) : i.parentNode[u]) <= b + 1)
        ),
        f &&
          ((l = ea(a)),
          f && (i.style[n.op.p] = l[n.op.p] - n.op.m - i._offset + qe));
    }
    return Math.round(e);
  }
  function Ma(l, e) {
    var c,
      f = C(l) ? e.sc : D(l, e),
      u = "_scroll" + e.p2;
    return (
      (l[u] = f),
      function getTween(e, t, r, n, o) {
        var i = getTween.tween,
          a = t.onComplete,
          s = {};
        return (
          i && i.kill(),
          (c = f()),
          (t[u] = e),
          ((t.modifiers = s)[u] = function (e) {
            return (
              f() !== c
                ? (i.kill(), (getTween.tween = 0), (e = f()))
                : n && (e = r + n * i.ratio + o * i.ratio * i.ratio),
              (c = Math.round(e))
            );
          }),
          (t.onComplete = function () {
            (getTween.tween = 0), a && a.call(i);
          }),
          (i = getTween.tween = xe.to(l, t))
        );
      }
    );
  }
  var xe,
    o,
    we,
    Te,
    x,
    Se,
    i,
    a,
    s,
    l,
    Ce,
    ke,
    _e,
    c,
    Me,
    Pe,
    f,
    Ee = 1,
    Oe = Date.now,
    u = Oe(),
    ze = 0,
    Fe = 1,
    Ae = Math.abs,
    t = "scrollLeft",
    r = "scrollTop",
    p = "left",
    d = "top",
    y = "right",
    w = "bottom",
    Ie = "width",
    Le = "height",
    De = "Right",
    Be = "Left",
    Ge = "Top",
    Ne = "Bottom",
    Re = "padding",
    je = "margin",
    He = "Width",
    We = "Height",
    qe = "px",
    Je = {
      s: t,
      p: p,
      p2: Be,
      os: y,
      os2: De,
      d: Ie,
      d2: He,
      a: "x",
      sc: function sc(e) {
        return arguments.length
          ? we.scrollTo(e, Xe.sc())
          : we.pageXOffset || Te[t] || x[t] || Se[t] || 0;
      },
    },
    Xe = {
      s: r,
      p: d,
      p2: Ge,
      os: w,
      os2: Ne,
      d: Le,
      d2: We,
      a: "y",
      op: Je,
      sc: function sc(e) {
        return arguments.length
          ? we.scrollTo(Je.sc(), e)
          : we.pageYOffset || Te[r] || x[r] || Se[r] || 0;
      },
    },
    Ye = {
      startColor: "green",
      endColor: "red",
      indent: 0,
      fontSize: "16px",
      fontWeight: "normal",
    },
    Ue = { toggleActions: "play", anticipatePin: 0 },
    g = { top: 0, left: 0, center: 0.5, bottom: 1, right: 1 },
    T = function _positionMarker(e, t, r, n, o) {
      var i = {},
        a = r[o ? "os2" : "p2"],
        s = r[o ? "p2" : "os2"];
      (e._isFlipped = o),
        (i[r.a + "Percent"] = o ? -100 : 0),
        (i[r.a] = o ? 1 : 0),
        (i["border" + a + He] = 1),
        (i["border" + s + He] = 0),
        (i[r.p] = t),
        xe.set(e, i);
    },
    Ve = [],
    Ze = {},
    h = {},
    n = [],
    v = function _dispatch(e) {
      return (
        (h[e] &&
          h[e].map(function (e) {
            return e();
          })) ||
        n
      );
    },
    m = function _updateAll() {
      var e = Ve.length,
        t = 0,
        r = Oe(),
        n = 50 <= r - u;
      for (
        n &&
        (ze && !Pe && 200 < r - ze && ((ze = 0), v("scrollEnd")),
        (_e = u),
        (u = r));
        t < e;
        t++
      )
        Ve[t] && Ve[t].update(0, n);
      l = 0;
    },
    b = [
      p,
      d,
      w,
      y,
      je + Ne,
      je + De,
      je + Ge,
      je + Be,
      "display",
      "flexShrink",
    ],
    S = b.concat([
      Ie,
      Le,
      "boxSizing",
      "max" + He,
      "max" + We,
      "position",
      je,
      Re,
      Re + Ge,
      Re + De,
      Re + Ne,
      Re + Be,
    ]),
    k = /([A-Z])/g,
    $e = function _setState(e) {
      for (var t, r, n = e.t.style, o = e.length, i = 0; i < o; i += 2)
        (r = e[i + 1]),
          (t = e[i]),
          r
            ? (n[t] = r)
            : n[t] && n.removeProperty(t.replace(k, "-$1").toLowerCase());
    },
    Ke = { left: 0, top: 0 },
    Qe = /(?:webkit|moz|length)/i;
  Je.op = Xe;
  var _ =
    ((ScrollTrigger.prototype.init = function init(y, x) {
      if (((this.progress = 0), this.vars && this.kill(1), Fe)) {
        var d,
          e,
          c,
          w,
          g,
          h,
          T,
          S,
          k,
          _,
          M,
          P,
          t,
          O,
          A,
          L,
          B,
          N,
          r,
          R,
          v,
          j,
          W,
          m,
          q,
          b,
          J,
          n,
          X,
          Y,
          U,
          V,
          o,
          f,
          Z,
          $,
          K = (y = da(F(y) || H(y) || y.nodeType ? { trigger: y } : y, Ue))
            .horizontal
            ? Je
            : Xe,
          Q = y.onUpdate,
          ee = y.toggleClass,
          i = y.id,
          te = y.onToggle,
          re = y.onRefresh,
          a = y.scrub,
          ne = y.trigger,
          oe = y.pin,
          ie = y.pinSpacing,
          ae = y.invalidateOnRefresh,
          se = y.anticipatePin,
          s = y.onScrubComplete,
          u = y.onSnapComplete,
          le = y.once,
          ce = y.snap,
          fe = y.pinReparent,
          ue = !a && 0 !== a,
          pe = Ce(y.scroller || we)[0],
          l = xe.core.getCache(pe),
          de = C(pe),
          ge = [y.onEnter, y.onLeave, y.onEnterBack, y.onLeaveBack],
          he = ue && (le ? "play" : y.toggleActions).split(" "),
          p = "markers" in y ? y.markers : Ue.markers,
          ve = de ? 0 : parseFloat(ba(pe)["border" + K.p2 + He]) || 0,
          me = this,
          be = function softRefresh() {
            return (
              ScrollTrigger.removeEventListener("scrollEnd", softRefresh) ||
              me.refresh()
            );
          },
          ye =
            y.onRefreshInit &&
            function () {
              return y.onRefreshInit(me);
            };
        (se *= 45),
          Ve.push(me),
          (me.scroller = pe),
          (me.scroll = de ? K.sc : D(pe, K)),
          (g = me.scroll()),
          (me.vars = y),
          (x = x || y.animation),
          (l.tweenScroll = l.tweenScroll || {
            top: Ma(pe, Xe),
            left: Ma(pe, Je),
          }),
          (me.tweenTo = d = l.tweenScroll[K.p]),
          x &&
            ((x.vars.lazy = !1),
            x._initted ||
              (!1 !== x.vars.immediateRender && x.render(-0.01, !0, !0)),
            (me.animation = x.pause()),
            (x.scrollTrigger = me),
            (o = H(a) && a) &&
              (V = xe.to(x, {
                ease: "power3",
                duration: o,
                onComplete: function onComplete() {
                  return s && s(me);
                },
              })),
            (X = 0),
            (i = i || x.vars.id)),
          ce &&
            (I(ce) || (ce = { snapTo: ce }),
            (c = G(ce.snapTo)
              ? ce.snapTo
              : "labels" === ce.snapTo
              ? (function _getLabels(i) {
                  return function (e) {
                    var t,
                      r = [],
                      n = i.labels,
                      o = i.duration();
                    for (t in n) r.push(n[t] / o);
                    return xe.utils.snap(r, e);
                  };
                })(x)
              : xe.utils.snap(ce.snapTo)),
            (f = ce.duration || { min: 0.1, max: 2 }),
            (f = I(f) ? ke(f.min, f.max) : ke(f, f)),
            (Z = xe
              .delayedCall(ce.delay || o / 2 || 0.1, function () {
                if (!ze || (ze === U && !Pe)) {
                  var e = x && !ue ? x.totalProgress() : me.progress,
                    t = ((e - Y) / (Oe() - _e)) * 1e3 || 0,
                    r = (Ae(t / 2) * t) / 0.185,
                    n = e + r,
                    o = ke(0, 1, c(n, me)),
                    i = o - e - r,
                    a = me.scroll(),
                    s = Math.round(T + o * O),
                    l = d.tween;
                  if (a <= S && T <= a) {
                    if (l && !l._initted) {
                      if (l.data <= Math.abs(s - a)) return;
                      l.kill();
                    }
                    d(
                      s,
                      {
                        duration: f(
                          Ae(
                            (0.185 * Math.max(Ae(n - e), Ae(o - e))) /
                              t /
                              0.05 || 0
                          )
                        ),
                        ease: ce.ease || "power3",
                        data: Math.abs(s - a),
                        onComplete: function onComplete() {
                          (X = Y = x && !ue ? x.totalProgress() : me.progress),
                            u && u(me);
                        },
                      },
                      T + e * O,
                      r * O,
                      i * O
                    );
                  }
                } else Z.restart(!0);
              })
              .pause())),
          i && (Ze[i] = me),
          (ne = me.trigger = Ce(ne || oe)[0]),
          (oe = !0 === oe ? ne : Ce(oe)[0]),
          F(ee) && (ee = { targets: ne, className: ee }),
          oe &&
            (!1 === ie ||
              ie === je ||
              (ie = "flex" !== ba(oe.parentNode).display && Re),
            (me.pin = oe),
            !1 !== y.force3D && xe.set(oe, { force3D: !0 }),
            (e = xe.core.getCache(oe)).spacer
              ? (A = e.pinState)
              : ((e.spacer = N = Te.createElement("div")),
                N.setAttribute(
                  "class",
                  "pin-spacer" + (i ? " pin-spacer-" + i : "")
                ),
                (e.pinState = A = Ga(oe))),
            (me.spacer = N = e.spacer),
            (n = ba(oe)),
            (m = n[ie + K.os2]),
            (R = xe.getProperty(oe)),
            (v = xe.quickSetter(oe, K.a, qe)),
            Da(oe, N, n),
            (B = Ga(oe))),
          p &&
            ((t = I(p) ? da(p, Ye) : Ye),
            (M = oa("scroller-start", i, pe, K, t, 0)),
            (P = oa("scroller-end", i, pe, K, t, 0, M)),
            (r = M["offset" + K.op.d2]),
            (k = oa("start", i, pe, K, t, r)),
            (_ = oa("end", i, pe, K, t, r)),
            de ||
              ((function _makePositionable(e) {
                e.style.position =
                  "absolute" === ba(e).position ? "absolute" : "relative";
              })(pe),
              xe.set([M, P], { force3D: !0 }),
              (b = xe.quickSetter(M, K.a, qe)),
              (J = xe.quickSetter(P, K.a, qe)))),
          (me.revert = function (e) {
            var t = !1 !== e;
            t !== w && (me.update(t), oe && t && Ca(oe, N, A), (w = t));
          }),
          (me.refresh = function (e) {
            if (!Me && $)
              if (oe && e && ze) ia(ScrollTrigger, "scrollEnd", be);
              else {
                var t = Math.max(me.scroll(), me.scroll.rec || 0),
                  r = me.progress;
                (Me = 1),
                  V && V.kill(),
                  ae && x && x.progress(0).invalidate().progress(me.progress),
                  w || me.revert();
                var n,
                  o,
                  i,
                  a,
                  s,
                  l,
                  c,
                  f = (de ? we["inner" + K.d2] : pe["client" + K.d2]) || 0,
                  u = de ? Ke : ea(pe),
                  p = E(pe, K),
                  d = 0,
                  g = 0,
                  h = y.end,
                  v = y.endTrigger || ne,
                  m = y.start || (oe || !ne ? "0 0" : "0 100%"),
                  b = (oe && Math.max(0, Ve.indexOf(me))) || 0;
                if (b) for (l = b; l--; ) Ve[l].pin === oe && Ve[l].revert();
                if (
                  ((T =
                    Ja(m, ne, f, K, me.scroll(), k, M, me, u, ve, de, p) ||
                    (oe ? -0.001 : 0)),
                  G(h) && (h = h(me)),
                  F(h) &&
                    !h.indexOf("+=") &&
                    (~h.indexOf(" ")
                      ? (h = (F(m) ? m.split(" ")[0] : "") + h)
                      : ((d = na(h.substr(2), f)),
                        (h = F(m) ? m : T + d),
                        (v = ne))),
                  (S =
                    Math.max(
                      T,
                      Ja(
                        h || (v ? "100% 0" : p),
                        v,
                        f,
                        K,
                        me.scroll() + d,
                        _,
                        P,
                        me,
                        u,
                        ve,
                        de,
                        p
                      )
                    ) || -0.001),
                  (O = S - T || ((T -= 0.01) && 0.001)),
                  oe)
                ) {
                  for (l = b; l--; )
                    (c = Ve[l]).pin === oe &&
                      c.start - c._pinPush < T &&
                      (g += c.end - c.start);
                  if (
                    ((T += g),
                    (S += g),
                    (me._pinPush = g),
                    k && g && (((n = {})[K.a] = "+=" + g), xe.set([k, _], n)),
                    (n = ba(oe)),
                    (a = K === Xe),
                    (i = me.scroll()),
                    (j = parseFloat(R(K.a)) + g),
                    Da(oe, N, n),
                    (B = Ga(oe)),
                    (o = ea(oe, !0)),
                    ie &&
                      ((N.style[ie + K.os2] = O + g + qe),
                      (q = ie === Re ? fa(oe, K) + O + g : 0) &&
                        (N.style[K.d] = q + qe),
                      de && me.scroll(t)),
                    de &&
                      (((s = {
                        top: o.top + (a ? i - T : 0) + qe,
                        left: o.left + (a ? 0 : i - T) + qe,
                        boxSizing: "border-box",
                        position: "fixed",
                      })[Ie] = s.maxWidth =
                        Math.ceil(o.width) + qe),
                      (s[Le] = s["max" + We] = Math.ceil(o.height) + qe),
                      (s[je] =
                        s[je + Ge] =
                        s[je + De] =
                        s[je + Ne] =
                        s[je + Be] =
                          "0"),
                      (s[Re] = n[Re]),
                      (s[Re + Ge] = n[Re + Ge]),
                      (s[Re + De] = n[Re + De]),
                      (s[Re + Ne] = n[Re + Ne]),
                      (s[Re + Be] = n[Re + Be]),
                      (L = (function _copyState(e, t, r) {
                        for (
                          var n, o = [], i = e.length, a = r ? 8 : 0;
                          a < i;
                          a += 2
                        )
                          (n = e[a]), o.push(n, n in t ? t[n] : e[a + 1]);
                        return (o.t = e.t), o;
                      })(A, s, fe))),
                    x
                      ? (x.progress(1, !0),
                        (W = R(K.a) - j + O + g),
                        O !== W && L.splice(L.length - 2, 2),
                        x.progress(0, !0))
                      : (W = O),
                    b)
                  )
                    for (l = 0; l < b; l++)
                      Ve[l].pin === oe && Ve[l].revert(!1);
                } else if (ne && me.scroll())
                  for (o = ne.parentNode; o && o !== Se; )
                    o._pinOffset && ((T -= o._pinOffset), (S -= o._pinOffset)),
                      (o = o.parentNode);
                (me.start = T),
                  (me.end = S),
                  me.scroll() < t && me.scroll(t),
                  me.revert(!1),
                  (Me = 0),
                  r !== me.progress &&
                    (V && x.totalProgress(r, !0),
                    (me.progress = r),
                    me.update()),
                  oe && ie && (N._pinOffset = Math.round(me.progress * W)),
                  re && re(me);
              }
          }),
          (me.getVelocity = function () {
            return ((me.scroll() - h) / (Oe() - _e)) * 1e3 || 0;
          }),
          (me.update = function (e, t) {
            var r,
              n,
              o,
              i,
              a,
              s = me.scroll(),
              l = e ? 0 : (s - T) / O,
              c = l < 0 ? 0 : 1 < l ? 1 : l || 0,
              f = me.progress;
            if (
              (t &&
                ((h = g),
                (g = s),
                ce && ((Y = X), (X = x && !ue ? x.totalProgress() : c))),
              se &&
                !c &&
                oe &&
                !Me &&
                T < s + ((s - h) / (Oe() - _e)) * se &&
                (c = 1e-4),
              c !== f && $)
            ) {
              if (
                ((i =
                  (a = (r = me.isActive = !!c && c < 1) != (!!f && f < 1)) ||
                  !!c != !!f),
                (me.direction = f < c ? 1 : -1),
                (me.progress = c),
                ue ||
                  (!V || Me || Ee
                    ? x && x.totalProgress(c, !!Me)
                    : ((V.vars.totalProgress = c), V.invalidate().restart())),
                oe)
              )
                if ((e && ie && (N.style[ie + K.os2] = m), de)) {
                  if (i) {
                    if (((o = !e && s + 1 >= E(pe, K)), fe)) {
                      if (!Me && (r || o)) {
                        var u = ea(oe, !0),
                          p = s - T;
                        (oe.style.top = u.top + (K === Xe ? p : 0) + qe),
                          (oe.style.left = u.left + (K === Xe ? 0 : p) + qe);
                      }
                      !(function _reparent(e, t) {
                        if (e.parentNode !== t) {
                          var r,
                            n,
                            o = e.style;
                          if (t === Se)
                            for (r in ((e._stOrig = o.cssText), (n = ba(e))))
                              +r ||
                                Qe.test(r) ||
                                !n[r] ||
                                "string" != typeof o[r] ||
                                "0" === r ||
                                (o[r] = n[r]);
                          else o.cssText = e._stOrig;
                          t.appendChild(e);
                        }
                      })(oe, Me || (!r && !o) ? N : Se);
                    }
                    $e(r || o ? L : B),
                      (W !== O && c < 1 && r) || v(j + (1 !== c || o ? 0 : W));
                  }
                } else v(j + W * c);
              !ce || d.tween || Me || Ee || ((U = ze), Z.restart(!0)),
                ee &&
                  a &&
                  (!le || r) &&
                  Ce(ee.targets).forEach(function (e) {
                    return e.classList[r ? "add" : "remove"](ee.className);
                  }),
                !Q || ue || e || Q(me),
                i && !Me
                  ? ((n = c && !f ? 0 : 1 === c ? 1 : 1 === f ? 2 : 3),
                    1 === c && le
                      ? me.kill()
                      : ue &&
                        ((o =
                          (!a && "none" !== he[n + 1] && he[n + 1]) || he[n]),
                        x &&
                          ("complete" === o || "reset" === o || o in x) &&
                          ("complete" === o
                            ? x.pause().totalProgress(1)
                            : "reset" === o
                            ? x.restart(!0).pause()
                            : x[o]()),
                        Q && Q(me)),
                    (!a && Ee) ||
                      (te && a && te(me),
                      ge[n] && ge[n](me),
                      le && (ge[n] = 0),
                      a || (ge[(n = 1 === c ? 1 : 3)] && ge[n](me))))
                  : ue && Q && !Me && Q(me);
            }
            J && (b(s + (M._isFlipped ? 1 : 0)), J(s));
          }),
          (me.enable = function () {
            $ ||
              (($ = !0),
              ia(pe, "resize", ua),
              ia(pe, "scroll", ta),
              ye && ia(ScrollTrigger, "refreshInit", ye),
              x &&
                (x.add
                  ? xe.delayedCall(0.01, me.refresh) &&
                    (O = 0.01) &&
                    (T = S = 0)
                  : me.refresh()));
          }),
          (me.disable = function (e) {
            if (
              $ &&
              (($ = me.isActive = !1),
              V && V.pause(),
              e !== $ && me.update(1),
              oe && Ca(oe, N, A),
              ye && ja(ScrollTrigger, "refreshInit", ye),
              Z && (Z.pause(), d.tween && d.tween.kill()),
              !de)
            ) {
              for (var t = Ve.length; t--; )
                if (Ve[t].scroller === pe && Ve[t] !== me) return;
              ja(pe, "resize", ua), ja(pe, "scroll", ta);
            }
          }),
          (me.kill = function (e) {
            me.disable(e),
              i && delete Ze[i],
              Ve.splice(Ve.indexOf(me), 1),
              x && (x.scrollTrigger = null);
          }),
          me.enable();
      } else this.update = this.refresh = this.kill = z;
    }),
    (ScrollTrigger.register = function register(e) {
      if (
        ((xe = e || B()),
        A() &&
          window.document &&
          ((we = window),
          (Te = document),
          (x = Te.documentElement),
          (Se = Te.body)),
        xe &&
          ((Ce = xe.utils.toArray),
          (ke = xe.utils.clamp),
          xe.core.globals("ScrollTrigger", ScrollTrigger),
          Se))
      ) {
        (s =
          we.requestAnimationFrame ||
          function (e) {
            return setTimeout(e, 16);
          }),
          ia(we, "mousewheel", ta),
          (i = [we, Te, x, Se]),
          ia(Te, "scroll", ta);
        var t,
          r = Se.style,
          n = r.borderTop;
        (r.borderTop = "1px solid #000"),
          (t = ea(Se)),
          (Xe.m = Math.round(t.top + Xe.sc()) || 0),
          (Je.m = Math.round(t.left + Je.sc()) || 0),
          n ? (r.borderTop = n) : r.removeProperty("border-top"),
          (c = setInterval(sa, 100)),
          xe.delayedCall(0.5, function () {
            return (Ee = 0);
          }),
          ia(Te, "touchcancel", z),
          ia(Se, "touchstart", z),
          ha(ia, Te, "pointerdown,touchstart,mousedown", function () {
            return (Pe = 1);
          }),
          ha(ia, Te, "pointerup,touchend,mouseup", function () {
            return (Pe = 0);
          }),
          (f = xe.utils.checkPrefix("transform")),
          S.push(f),
          (o = Oe()),
          (a = xe.delayedCall(0.2, ya).pause()),
          ia(Te, "visibilitychange", function () {
            return Te.hidden || ya();
          }),
          ia(Te, "DOMContentLoaded", ya),
          ia(we, "load", function () {
            return ze || ya();
          }),
          ia(we, "resize", ua);
      }
      return o;
    }),
    (ScrollTrigger.defaults = function defaults(e) {
      for (var t in e) Ue[t] = e[t];
    }),
    (ScrollTrigger.kill = function kill() {
      (Fe = 0),
        Ve.slice(0).forEach(function (e) {
          return e.kill(1);
        });
    }),
    ScrollTrigger);
  function ScrollTrigger(e, t) {
    o ||
      ScrollTrigger.register(xe) ||
      console.warn("Please gsap.registerPlugin(ScrollTrigger)"),
      this.init(e, t);
  }
  (_.version = "3.3.3"),
    (_.create = function (e, t) {
      return new _(e, t);
    }),
    (_.refresh = function (e) {
      return e ? ua() : ya(!0);
    }),
    (_.update = m),
    (_.maxScroll = function (e, t) {
      return E(e, t ? Je : Xe);
    }),
    (_.getScrollFunc = function (e, t) {
      return (t = t ? Je : Xe) && (C(e) ? t.sc : D(e, t));
    }),
    (_.getById = function (e) {
      return Ze[e];
    }),
    (_.getAll = function () {
      return Ve.slice(0);
    }),
    (_.syncInterval = function (e) {
      return clearInterval(c) || ((c = e) && setInterval(sa, e));
    }),
    (_.isScrolling = function () {
      return !!ze;
    }),
    (_.addEventListener = function (e, t) {
      var r = h[e] || (h[e] = []);
      ~r.indexOf(t) || r.push(t);
    }),
    (_.removeEventListener = function (e, t) {
      var r = h[e],
        n = r && r.indexOf(t);
      0 <= n && r.splice(n, 1);
    }),
    (_.batch = function (e, t) {
      function wg(e, t) {
        var r = [],
          n = [],
          o = xe
            .delayedCall(i, function () {
              t(r, n), (r = []), (n = []);
            })
            .pause();
        return function (e) {
          r.length || o.restart(!0),
            r.push(e.trigger),
            n.push(e),
            a <= r.length && o.progress(1);
        };
      }
      var r,
        n = [],
        o = {},
        i = t.interval || 0.016,
        a = t.batchMax || 1e9;
      for (r in t)
        o[r] =
          "on" === r.substr(0, 2) && G(t[r]) && "onRefreshInit" !== r
            ? wg(0, t[r])
            : t[r];
      return (
        G(a) &&
          ((a = a()),
          _.addEventListener("refresh", function () {
            return (a = t.batchMax());
          })),
        Ce(e).forEach(function (e) {
          var t = {};
          for (r in o) t[r] = o[r];
          (t.trigger = e), n.push(_.create(t));
        }),
        n
      );
    }),
    B() && xe.registerPlugin(_),
    (e.ScrollTrigger = _),
    (e.default = _);
  if (typeof window === "undefined" || window !== e) {
    Object.defineProperty(e, "__esModule", { value: !0 });
  } else {
    delete e.default;
  }
});

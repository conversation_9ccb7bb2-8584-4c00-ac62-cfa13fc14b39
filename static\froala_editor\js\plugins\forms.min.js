/*!
 * froala_editor v4.0.10 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],e):e(t.FroalaEditor)}(this,function(t){"use strict";t=t&&t.hasOwnProperty("default")?t["default"]:t,Object.assign(t.POPUP_TEMPLATES,{"forms.edit":"[_BUTTONS_]","forms.update":"[_BUTTONS_][_TEXT_LAYER_]"}),Object.assign(t.DEFAULTS,{formEditButtons:["inputStyle","inputEdit"],formStyles:{"fr-rounded":"Rounded","fr-large":"Large"},formMultipleStyles:!0,formUpdateButtons:["inputBack","|"]}),t.PLUGINS.forms=function(s){var u,r=s.$;function e(t){s.selection.clear(),r(this).data("mousedown",!0)}function o(t){r(this).data("mousedown")&&(t.stopPropagation(),r(this).removeData("mousedown"),f(u=this)),t.preventDefault()}function n(){s.$el.find("input, textarea, button").removeData("mousedown")}function a(){r(this).removeData("mousedown")}function p(){return u||null}function f(t){if(-1==["checkbox","radio"].indexOf(t.type)){var e=s.popups.get("forms.edit");e||(e=function i(){var t="";0<s.opts.formEditButtons.length&&(t='<div class="fr-buttons">'.concat(s.button.buildList(s.opts.formEditButtons),"</div>"));var e={buttons:t},o=s.popups.create("forms.edit",e);return s.$wp&&s.events.$on(s.$wp,"scroll.link-edit",function(){p()&&s.popups.isVisible("forms.edit")&&f(p())}),o}());var o=r(u=t);s.popups.refresh("forms.edit"),s.popups.setContainer("forms.edit",s.$sc);var n=o.offset().left+o.outerWidth()/2,a=o.offset().top+o.outerHeight();s.popups.show("forms.edit",n,a,o.outerHeight())}}function i(){var t=s.popups.get("forms.update"),e=p();if(e){var o=r(e);o.is("button")?t.find('input[type="text"][name="text"]').val(o.text()):o.is("input[type=button]")||o.is("input[type=submit]")||o.is("input[type=reset]")?t.find('input[type="text"][name="text"]').val(o.val()):t.find('input[type="text"][name="text"]').val(o.attr("placeholder"))}t.find('input[type="text"][name="text"]').trigger("change")}function d(){u=null}function l(t){if(t)return s.popups.onRefresh("forms.update",i),s.popups.onHide("forms.update",d),!0;var e="";1<=s.opts.formUpdateButtons.length&&(e='<div class="fr-buttons">'.concat(s.button.buildList(s.opts.formUpdateButtons),"</div>"));var o=0,n={buttons:e,text_layer:'<div class="fr-forms-text-layer fr-layer fr-active"> \n    <div class="fr-input-line"><input name="text" type="text" placeholder="Text" tabIndex=" '.concat(++o,' "></div>\n    <div class="fr-action-buttons"><button class="fr-command fr-submit" data-cmd="updateInput" href="#" tabIndex="').concat(2,'" type="button">').concat(s.language.translate("Update"),"</button></div></div>")};return s.popups.create("forms.update",n)}return{_init:function c(){!function t(){s.events.$on(s.$el,s._mousedown,"input, textarea, button",e),s.events.$on(s.$el,s._mouseup,"input, textarea, button",o),s.events.$on(s.$el,"touchmove","input, textarea, button",a),s.events.$on(s.$el,s._mouseup,n),s.events.$on(s.$win,s._mouseup,n),l(!0)}(),s.events.$on(s.$el,"submit","form",function(t){return t.preventDefault(),!1})},updateInput:function m(){var t=s.popups.get("forms.update"),e=p();if(e){var o=r(e),n=t.find('input[type="text"][name="text"]').val()||"";o.is("button")?n.length?o.text(n):o.text("\u200b"):-1!=["button","submit","reset"].indexOf(e.type)?o.attr("value",n):o.attr("placeholder",n),s.popups.hide("forms.update"),f(e)}},getInput:p,applyStyle:function v(t,e,o){void 0===e&&(e=s.opts.formStyles),void 0===o&&(o=s.opts.formMultipleStyles);var n=p();if(!n)return!1;if(!o){var a=Object.keys(e);a.splice(a.indexOf(t),1),r(n).removeClass(a.join(" "))}r(n).toggleClass(t)},showUpdatePopup:function h(){var t=p();if(t){var e=r(t),o=s.popups.get("forms.update");o||(o=l()),s.popups.isVisible("forms.update")||s.popups.refresh("forms.update"),s.popups.setContainer("forms.update",s.$sc);var n=e.offset().left+e.outerWidth()/2,a=e.offset().top+e.outerHeight();s.popups.show("forms.update",n,a,e.outerHeight())}},showEditPopup:f,back:function b(){s.events.disableBlur(),s.selection.restore(),s.events.enableBlur();var t=p();t&&s.$wp&&("BUTTON"===t.tagName&&s.selection.restore(),f(t))}}},t.RegisterCommand("updateInput",{undo:!1,focus:!1,title:"Update",callback:function(){this.forms.updateInput()}}),t.DefineIcon("inputStyle",{NAME:"magic",SVG_KEY:"inlineStyle"}),t.RegisterCommand("inputStyle",{title:"Style",type:"dropdown",html:function(){var t='<ul class="fr-dropdown-list">',e=this.opts.formStyles;for(var o in e)e.hasOwnProperty(o)&&(t+='<li><a class="fr-command" tabIndex="-1" data-cmd="inputStyle" data-param1="'.concat(o,'">').concat(this.language.translate(e[o]),"</a></li>"));return t+="</ul>"},callback:function(t,e){var o=this.forms.getInput();o&&(this.forms.applyStyle(e),this.forms.showEditPopup(o))},refreshOnShow:function(t,e){var o=this.$,n=this.forms.getInput();if(n){var a=o(n);e.find(".fr-command").each(function(){var t=o(this).data("param1");o(this).toggleClass("fr-active",a.hasClass(t))})}}}),t.DefineIcon("inputEdit",{NAME:"edit",SVG_KEY:"edit"}),t.RegisterCommand("inputEdit",{title:"Edit Button",undo:!1,refreshAfterCallback:!1,callback:function(){this.forms.showUpdatePopup()}}),t.DefineIcon("inputBack",{NAME:"arrow-left",SVG_KEY:"back"}),t.RegisterCommand("inputBack",{title:"Back",undo:!1,focus:!1,back:!0,refreshAfterCallback:!1,callback:function(){this.forms.back()}}),t.RegisterCommand("updateInput",{undo:!1,focus:!1,title:"Update",callback:function(){this.forms.updateInput()}})});
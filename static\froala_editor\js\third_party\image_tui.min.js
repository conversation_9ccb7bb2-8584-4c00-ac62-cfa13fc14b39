/*!
 * froala_editor v4.0.10 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(e){"use strict";function v(e){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}if(e=e&&e.hasOwnProperty("default")?e["default"]:e,Object.assign(e.DEFAULTS,{imageTUIOptions:{includeUI:{theme:{"menu.normalIcon.path":"https://cdn.jsdelivr.net/npm/tui-image-editor@3.2.2/dist/svg/icon-d.svg","menu.activeIcon.path":"https://cdn.jsdelivr.net/npm/tui-image-editor@3.2.2/dist/svg/icon-b.svg","menu.disabledIcon.path":"https://cdn.jsdelivr.net/npm/tui-image-editor@3.2.2/dist/svg/icon-a.svg","menu.hoverIcon.path":"https://cdn.jsdelivr.net/npm/tui-image-editor@3.2.2/dist/svg/icon-c.svg","submenu.normalIcon.path":"https://cdn.jsdelivr.net/npm/tui-image-editor@3.2.2/dist/svg/icon-d.svg","submenu.normalIcon.name":"icon-d","submenu.activeIcon.path":"https://cdn.jsdelivr.net/npm/tui-image-editor@3.2.2/dist/svg/icon-c.svg","submenu.activeIcon.name":"icon-c"},initMenu:"filter",menuBarPosition:"left"}},tui:window.tui}),e.PLUGINS.imageTUI=function(c){var l=c.$,m=!0;function g(e,t){l("#tuieditor").remove(),e.style.display="none",m||t===undefined||t.filesManager.setChildWindowState(!1)}function p(e,t,i,n,o){for(var a=e.toDataURL(),d=atob(a.split(",")[1]),r=[],s=0;s<d.length;s++)r.push(d.charCodeAt(s));var u=new Blob([new Uint8Array(r)],{type:"image/png"});n?(t.image.edit(i),t.image.upload([u])):(t.filesManager.saveImage([u]),null!=o?(t.filesManager.upload(u,[u],null,o),t.filesManager.getFileThumbnail(o,u,!0)):t.filesManager.upload(u,[u],null,i))}return{_init:function i(){var e=c.o_doc.body,t=c.o_doc.createElement("div");t.setAttribute("id","tuiContainer"),t.style.cssText="position: fixed; top: 0;left: 0;margin: 0;padding: 0;width: 100%;height: 100%;background: rgba(0,0,0,.5);z-index: 9998;display:none",e.appendChild(t)},launch:function f(t,i,n){var o,e;if(m=i,"object"===v(c.opts.tui)){var a=c.o_doc.createElement("div");a.setAttribute("id","tuieditor");var d=c.o_doc.getElementById("tuiContainer");d.appendChild(a),d.style.display="block",e=i?(o=t.image.get())[0].src:(o=t.filesManager.get()).src;var r=c.opts.imageTUIOptions;r.includeUI.loadImage={path:e,name:" "};var s=new c.opts.tui.ImageEditor(c.o_doc.querySelector("#tuieditor"),r),u=c.o_doc.getElementById("tuieditor");u.style.minHeight="590px",u.style.width="94%",u.style.height="94%",u.style.margin="auto",l(".tui-image-editor-header-buttons").html('<button class="tui-editor-cancel-btn" data-cmd="cancel_tui_image">Cancel</button> <button class="tui-editor-save-btn">Save</button>'),l(".tui-editor-cancel-btn")[0].addEventListener("click",function(e){g(d,t)}),l(".tui-editor-save-btn")[0].addEventListener("click",function(e){null!=n?p(s,t,o,i,n):p(s,t,o,i),g(d,t)})}}}},e.DefineIcon("imageTUI",{NAME:"sliders",FA5NAME:"sliders-h",SVG_KEY:"advancedImageEditor"}),e.RegisterCommand("imageTUI",{title:"Advanced Edit",undo:!1,focus:!1,callback:function(e,t){this.imageTUI.launch(this,!0)},plugin:"imageTUI"}),!e.PLUGINS.image)throw new Error("TUI image editor plugin requires image plugin.");e.DEFAULTS.imageEditButtons.push("imageTUI")});
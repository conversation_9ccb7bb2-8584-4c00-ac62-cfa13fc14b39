/*!
 * froala_editor v4.0.10 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("froala-editor")):"function"==typeof define&&define.amd?define(["froala-editor"],t):t(e.FroalaEditor)}(this,function(It){"use strict";function Lt(e){return(Lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}It=It&&It.hasOwnProperty("default")?It["default"]:It,Object.assign(It.POPUP_TEMPLATES,{"filesManager.insert":"[_BUTTONS_][_UPLOAD_LAYER_][_BY_URL_LAYER_][_EMBED_LAYER_][_UPLOAD_PROGRESS_LAYER_][_PROGRESS_BAR_]","image.edit":"[_BUTTONS_]","image.alt":"[_BUTTONS_][_ALT_LAYER_]","image.size":"[_BUTTONS_][_SIZE_LAYER_]"}),Object.assign(It.DEFAULTS,{filesInsertButtons:["imageBack","|","filesUpload","filesByURL","filesEmbed"],filesInsertButtons2:["deleteAll","insertAll","cancel","minimize"],imageEditButtons:["imageReplace","imageAlign","imageCaption","imageRemove","imageLink","linkOpen","linkEdit","linkRemove","-","imageDisplay","imageStyle","imageAlt","imageSize"],imageAltButtons:["imageBack","|"],imageSizeButtons:["imageBack","|"],imageUpload:!0,filesManagerUploadURL:null,imageCORSProxy:"https://cors-anywhere.froala.com",imageUploadRemoteUrls:!0,filesManagerUploadParam:"file",filesManagerUploadParams:{},googleOptions:{},filesManagerUploadToS3:!1,filesManagerUploadToAzure:!1,filesManagerUploadMethod:"POST",filesManagerMaxSize:10485760,filesManagerAllowedTypes:["*"],imageResize:!0,imageResizeWithPercent:!1,imageRoundPercent:!1,imageDefaultWidth:300,imageDefaultAlign:"center",imageDefaultDisplay:"block",imageSplitHTML:!1,imageStyles:{"fr-rounded":"Rounded","fr-bordered":"Bordered","fr-shadow":"Shadow"},imageMove:!0,imageMultipleStyles:!0,imageTextNear:!0,imagePaste:!0,imagePasteProcess:!1,imageMinWidth:16,imageOutputSize:!1,imageDefaultMargin:5,imageAddNewLine:!1}),It.VIDEO_PROVIDERS=[{test_regex:/^.*((youtu.be)|(youtube.com))\/((v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))?\??v?=?([^#\&\?]*).*/,url_regex:/(?:https?:\/\/)?(?:www\.)?(?:m\.)?(?:youtube\.com|youtu\.be)\/(?:watch\?v=|embed\/)?([0-9a-zA-Z_\-]+)(.+)?/g,url_text:"https://www.youtube.com/embed/$1?$2",html:'<iframe width="640" height="360" src="{url}&wmode=opaque" frameborder="0" allowfullscreen></iframe>',provider:"youtube"},{test_regex:/^.*(?:vimeo.com)\/(?:channels(\/\w+\/)?|groups\/*\/videos\/\u200b\d+\/|video\/|)(\d+)(?:$|\/|\?)/,url_regex:/(?:https?:\/\/)?(?:www\.|player\.)?vimeo.com\/(?:channels\/(?:\w+\/)?|groups\/(?:[^\/]*)\/videos\/|album\/(?:\d+)\/video\/|video\/|)(\d+)(?:[a-zA-Z0-9_\-]+)?(\/[a-zA-Z0-9_\-]+)?/i,url_text:"https://player.vimeo.com/video/$1",html:'<iframe width="640" height="360" src="{url}" frameborder="0" allowfullscreen></iframe>',provider:"vimeo"},{test_regex:/^.+(dailymotion.com|dai.ly)\/(video|hub)?\/?([^_]+)[^#]*(#video=([^_&]+))?/,url_regex:/(?:https?:\/\/)?(?:www\.)?(?:dailymotion\.com|dai\.ly)\/(?:video|hub)?\/?(.+)/g,url_text:"https://www.dailymotion.com/embed/video/$1",html:'<iframe width="640" height="360" src="{url}" frameborder="0" allowfullscreen></iframe>',provider:"dailymotion"},{test_regex:/^.+(screen.yahoo.com)\/[^_&]+/,url_regex:"",url_text:"",html:'<iframe width="640" height="360" src="{url}?format=embed" frameborder="0" allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true" allowtransparency="true"></iframe>',provider:"yahoo"},{test_regex:/^.+(rutube.ru)\/[^_&]+/,url_regex:/(?:https?:\/\/)?(?:www\.)?(?:rutube\.ru)\/(?:video)?\/?(.+)/g,url_text:"https://rutube.ru/play/embed/$1",html:'<iframe width="640" height="360" src="{url}" frameborder="0" allowfullscreen="true" mozallowfullscreen="true" webkitallowfullscreen="true" allowtransparency="true"></iframe>',provider:"rutube"},{test_regex:/^(?:.+)vidyard.com\/(?:watch)?\/?([^.&/]+)\/?(?:[^_.&]+)?/,url_regex:/^(?:.+)vidyard.com\/(?:watch)?\/?([^.&/]+)\/?(?:[^_.&]+)?/g,url_text:"https://play.vidyard.com/$1",html:'<iframe width="640" height="360" src="{url}" frameborder="0" allowfullscreen></iframe>',provider:"vidyard"}],It.VIDEO_EMBED_REGEX=/^\W*((<iframe(.|\n)*>(\s|\n)*<\/iframe>)|(<embed(.|\n)*>))\W*$/i,It.IMAGE_EMBED_REGEX=/^\W*((<img(.|\n)*>(\s|\n)*))\W*$/i,It.IMAGE_TYPE="image/png",It.IMAGE_ALLOW_REGEX=/\.(gif|jpe?g|tiff?|png|webp|bmp)$/i,It.PLUGINS.filesManager=function(A){var x,o,C,l,d,f,s,c,I=A.$,M="https://i.froala.com/upload",p=!1,t=!1,L=new Map,_=new Map,E=new Map,k=new Map,D=new Map,S=new Map,r=0,a=[],g=-1,T=[],u=0,m=["file","image","imageTUI","video"],B=1,v=2,R=3,U=4,z=5,O=6,$=10,P=["video/avi","video/mpeg","video/x-ms-wmv"],h={};function b(){var e=A.popups.get("filesManager.insert"),t=e.find(".fr-files-by-url-layer input");t.val(""),e.find(".fr-files-embed-layer textarea").val("").trigger("change"),t.trigger("change")}function y(e){var t;A.hasOwnProperty("imageTUI")||(s="fr-disabled"),S.forEach(function(e,t){q(t)}),w()?(t=A.popups.get("filesManager.insert"))||(t=Fe()):(t=A.popups.get("filesManager.insert"))||(t=function o(){var e='<div class="fr-buttons fr-tabs">'.concat(A.button.buildList(A.opts.fileInsertButtons),"</div>"),t="<div style= 'padding:10px'>\n    <div class = 'fr-message'><h3 style ='font-size: 16px; margin: 10px 10px;font-weight: normal;'>".concat(A.language.translate(function a(){var e="",t=function i(){var t=[];return m.forEach(function(e){A.opts.pluginsEnabled.indexOf(e)<0&&t.push(e.charAt(0).toUpperCase()+e.slice(1))}),t}();e=t.join(", "),1<t.length?e+=" plugin are":e+=" plugin is";return e}()+" not enabled. Do you want to enable?"),"</h3></div>\n    <div style='text-align:right;'>\n      <button class='fr-trim-button fr-plugins-enable'>").concat(A.language.translate("Enable"),"</button>               \n      <button class='fr-trim-button fr-plugins-cancel'>").concat(A.language.translate("Cancel"),"</button>\n    </div>"),i={buttons:e,upload_layer:t,by_url_layer:"",embed_layer:"",upload_progress_layer:"",progress_bar:""},n=A.popups.create("filesManager.insert",i);return Be(n),n}());var i=A.$tb.find('.fr-command[data-cmd="insertFiles"]');if(Q(),e||!t.hasClass("fr-active"))if(e||Z(),A.popups.refresh("filesManager.insert"),A.popups.setContainer("filesManager.insert",A.$tb),i.isVisible()){var n=A.button.getPosition(i,L.size),a=n.left,r=n.top;A.popups.show("filesManager.insert",a,r,i.outerHeight())}else A.position.forSelection(t),A.popups.show("filesManager.insert");A.popups.setPopupDimensions(t),w()&&A.popups.setFileListHeight(t),t.find(".fr-upload-progress")&&0==L.size&&t.find(".fr-upload-progress").addClass("fr-none")}function w(){var t=!0;return m.forEach(function(e){A.opts.pluginsEnabled.indexOf(e)<0&&(t=!1)}),t}function F(){Q()}function N(){if(l||function o(){var e;A.shared.$image_resizer?(l=A.shared.$image_resizer,f=A.shared.$img_overlay,A.events.on("destroy",function(){I("body").first().append(l.removeClass("fr-active"))},!0)):(A.shared.$image_resizer=I(document.createElement("div")).attr("class","fr-image-resizer"),l=A.shared.$image_resizer,A.events.$on(l,"mousedown",function(e){e.stopPropagation()},!0),A.opts.imageResize&&(l.append(H("nw")+H("ne")+H("sw")+H("se")),A.shared.$img_overlay=I(document.createElement("div")).attr("class","fr-image-overlay"),f=A.shared.$img_overlay,e=l.get(0).ownerDocument,I(e).find("body").first().append(f)));A.events.on("shared.destroy",function(){l.html("").removeData().remove(),l=null,A.opts.imageResize&&(f.remove(),f=null)},!0),A.helpers.isMobile()||A.events.$on(I(A.o_win),"resize",function(){x&&!x.hasClass("fr-uploading")?it(!0):x&&(N(),replace(),J(!1))});if(A.opts.imageResize){e=l.get(0).ownerDocument,A.events.$on(l,A._mousedown,".fr-handler",Y),A.events.$on(I(e),A._mousemove,V),A.events.$on(I(e.defaultView||e.parentWindow),A._mouseup,W),A.events.$on(f,"mouseleave",W);var n=1,a=null,r=0;A.events.on("keydown",function(e){if(x){var t=-1!=navigator.userAgent.indexOf("Mac OS X")?e.metaKey:e.ctrlKey,i=e.which;(i!==a||200<e.timeStamp-r)&&(n=1),(i==It.KEYCODE.EQUALS||A.browser.mozilla&&i==It.KEYCODE.FF_EQUALS)&&t&&!e.altKey?n=Xe.call(this,e,1,1,n):(i==It.KEYCODE.HYPHEN||A.browser.mozilla&&i==It.KEYCODE.FF_HYPHEN)&&t&&!e.altKey?n=Xe.call(this,e,2,-1,n):A.keys.ctrlKey(e)||i!=It.KEYCODE.ENTER||(x.before("<br>"),Ee(x)),a=i,r=e.timeStamp}},!0),A.events.on("keyup",function(){n=1})}}(),!x)return!1;var e=A.$wp||A.$sc;e.append(l),l.data("instance",A);var t=e.scrollTop()-("static"!=e.css("position")?e.offset().top:0),i=e.scrollLeft()-("static"!=e.css("position")?e.offset().left:0);i-=A.helpers.getPX(e.css("border-left-width")),t-=A.helpers.getPX(e.css("border-top-width")),A.$el.is("img")&&A.$sc.is("body")&&(i=t=0);var n=lt();dt()&&(n=n.find(".fr-img-wrap"));var a=0,r=0;A.opts.iframe&&(a=A.helpers.getPX(A.$wp.find(".fr-iframe").css("padding-top")),r=A.helpers.getPX(A.$wp.find(".fr-iframe").css("padding-left"))),l.css("top",(A.opts.iframe?n.offset().top+a:n.offset().top+t)-1).css("left",(A.opts.iframe?n.offset().left+r:n.offset().left+i)-1).css("width",n.get(0).getBoundingClientRect().width).css("height",n.get(0).getBoundingClientRect().height).addClass("fr-active")}function H(e){return'<div class="fr-handler fr-h'.concat(e,'"></div>')}function K(e){dt()?x.parents(".fr-img-caption").css("width",e):x.css("width",e)}function Y(e){if(!A.core.sameInstance(l))return!0;if(e.preventDefault(),e.stopPropagation(),A.$el.find("img.fr-error").left)return!1;A.undo.canDo()||A.undo.saveStep();var t=e.pageX||e.originalEvent.touches[0].pageX;if("mousedown"==e.type){var i=A.$oel.get(0).ownerDocument,n=i.defaultView||i.parentWindow,a=!1;try{a=n.location!=n.parent.location&&!(n.$&&n.$.FE)}catch(s){}a&&n.frameElement&&(t+=A.helpers.getPX(I(n.frameElement).offset().left)+n.frameElement.clientLeft)}(d=I(this)).data("start-x",t),d.data("start-width",x.width()),d.data("start-height",x.height());var r=x.width();if(A.opts.imageResizeWithPercent){var o=x.parentsUntil(A.$el,A.html.blockTagsQuery()).get(0)||A.el;r=(r/I(o).outerWidth()*100).toFixed(2)+"%"}K(r),f.show(),A.popups.hideAll(),rt()}function V(e){if(!A.core.sameInstance(l))return!0;var t;if(d&&x){if(e.preventDefault(),A.$el.find("img.fr-error").left)return!1;var i=e.pageX||(e.originalEvent.touches?e.originalEvent.touches[0].pageX:null);if(!i)return!1;var n=i-d.data("start-x"),a=d.data("start-width");if((d.hasClass("fr-hnw")||d.hasClass("fr-hsw"))&&(n=0-n),A.opts.imageResizeWithPercent){var r=x.parentsUntil(A.$el,A.html.blockTagsQuery()).get(0)||A.el;a=((a+n)/I(r).outerWidth()*100).toFixed(2),A.opts.imageRoundPercent&&(a=Math.round(a)),K("".concat(a,"%")),(t=dt()?(A.helpers.getPX(x.parents(".fr-img-caption").css("width"))/I(r).outerWidth()*100).toFixed(2):(A.helpers.getPX(x.css("width"))/I(r).outerWidth()*100).toFixed(2))===a||A.opts.imageRoundPercent||K("".concat(t,"%")),x.css("height","").removeAttr("height")}else a+n>=A.opts.imageMinWidth&&(K(a+n),t=dt()?A.helpers.getPX(x.parents(".fr-img-caption").css("width")):A.helpers.getPX(x.css("width"))),t!==a+n&&K(t),((x.attr("style")||"").match(/(^height:)|(; *height:)/)||x.attr("height"))&&(x.css("height",d.data("start-height")*x.width()/d.data("start-width")),x.removeAttr("height"));N(),A.events.trigger("image.resize",[st()])}}function W(e){if(!A.core.sameInstance(l))return!0;if(d&&x){if(e&&e.stopPropagation(),A.$el.find("img.fr-error").left)return!1;d=null,f.hide(),N(),A.undo.saveStep(),A.events.trigger("image.resizeEnd",[st()])}else l.removeClass("fr-active")}function G(){S.forEach(function(e,t){var i=A.popups.get("filesManager.insert");i.find(".fr-checkbox-file-"+t).get(0).disabled=!0,document.getElementById("fr-file-autoplay-button-"+t)&&(document.getElementById("fr-file-autoplay-button-"+t).disabled=!0,document.getElementById("fr-file-autoplay-button-"+t).parentElement.classList.add("fr-checkbox-disabled"),document.getElementById("fr-file-autoplay-button-"+t).parentElement.classList.remove("fr-files-checkbox")),i.find(".fr-checkbox-"+t).get(0).classList.remove("fr-files-checkbox"),i.find(".fr-checkbox-"+t).get(0).classList.add("fr-checkbox-disabled")})}function X(e,t,i,n){A.edit.on(),x&&x.addClass("fr-error"),h[e]?(e!=R&&e!=v&&e!=U||we(100,n,!0),S.set(n,h[e]),G(),function a(){S.forEach(function(e,t){A.popups.get("filesManager.insert"),document.getElementById("fr-file-edit-button-".concat(t))&&(document.getElementById("fr-file-edit-button-".concat(t)).classList.add("fr-disabled"),document.getElementById("fr-file-view-button-".concat(t)).classList.add("fr-disabled"),document.getElementById("fr-file-insert-button-".concat(t)).classList.add("fr-disabled"))})}(),ie(A.language.translate(h[e]),n)):ie(A.language.translate("Something went wrong. Please try again."),n),!x&&i&&je(i),A.events.trigger("filesManager.error",[{code:e,message:h[e]},t,i])}function j(){var e=A.popups.get("filesManager.insert"),t=e.find('.fr-command[data-cmd="insertAll"]'),i=e.find('.fr-command[data-cmd="deleteAll"]'),n=!0;D.forEach(function a(e,t,i){D.get(t)&&(n=!1)}),n?t.addClass("fr-disabled"):t.removeClass("fr-disabled"),n?i.addClass("fr-disabled"):i.removeClass("fr-disabled")}function q(e){_.get(e)&&_.get(e).link&&A.events.trigger("filesManager.removed",[_.get(e).link]);var t=A.popups.get("filesManager.insert");t.find(".fr-file-"+e).get(0)!==undefined&&(t.find(".fr-file-"+e).get(0).outerHTML=""),_["delete"](e),L["delete"](e),D["delete"](e),j(),0==L.size&&(u=0),S["delete"](e),A.popups.setPopupDimensions(t,!0),A.opts.toolbarBottom?y(!0):A.popups.setPopupDimensions(t),t.find(".fr-upload-progress")&&0==L.size&&t.find(".fr-upload-progress").addClass("fr-none")}function Z(){for(var e=A.popups.get("filesManager.insert"),t=e.find(".fr-insert-checkbox"),i=0;i<t.length;i++)t.get(i).children.target.checked=!1,e.find(".fr-file-"+t.get(i).id.split("-").pop()).get(0).classList.add("fr-unchecked");if(C)document.getElementById("fr-file-autoplay-button-".concat(C))&&(document.getElementById("fr-file-autoplay-button-".concat(C)).checked=!1),T=T.filter(function(e){return e!=C});else{for(var n=e.find(".fr-file-autoplay-button"),a=0;a<n.length;a++)n.get(a).checked=!1;T=[]}D=new Map,j()}function J(e){var t=A.popups.get("filesManager.insert");if(t||(t=Fe()),t.find(".fr-layer.fr-active").removeClass("fr-active").addClass("fr-pactive"),t.find(".fr-files-progress-bar-layer").addClass("fr-active"),t.find(".fr-buttons").hide(),x){var i=lt();A.popups.setContainer("filesManager.insert",A.$sc);var n=i.offset().left,a=i.offset().top+i.height();A.popups.show("filesManager.insert",n,a,i.outerHeight())}void 0===e&&ee(A.language.translate("Uploading"),0)}function Q(e){var t=A.popups.get("filesManager.insert");if(t&&(t.find(".fr-layer.fr-pactive").addClass("fr-active").removeClass("fr-pactive"),t.find(".fr-files-progress-bar-layer").removeClass("fr-active"),t.find(".fr-buttons").show(),e||A.$el.find("img.fr-error").length)){if(A.events.focus(),A.$el.find("img.fr-error").length&&(A.$el.find("img.fr-error").remove(),A.undo.saveStep(),A.undo.run(),A.undo.dropRedo()),!A.$wp&&x){var i=x;it(!0),A.selection.setAfter(i.get(0)),A.selection.restore()}A.popups.hide("filesManager.insert")}}function ee(e,t){var i=A.popups.get("filesManager.insert");if(i){var n=i.find(".fr-files-progress-bar-layer");n.find("h3").text(e+(t?" ".concat(t,"%"):"")),n.removeClass("fr-error"),t?(n.find("div").removeClass("fr-indeterminate"),n.find("div > span").css("width","".concat(t,"%"))):n.find("div").addClass("fr-indeterminate")}}function te(e){J();var t=A.popups.get("filesManager.insert").find(".fr-files-progress-bar-layer");t.addClass("fr-error");var i=t.find("h3");i.text(e),A.events.disableBlur(),i.focus()}function ie(e,t){var i=A.popups.get("filesManager.insert"),n=i.find(".fr-upload-progress-layer"),a=i.find(".fr-file-".concat(t));n.addClass("fr-error"),a.find("h5").text(e)}h[B]="File cannot be loaded from the passed link.",h[v]="No link in upload response.",h[R]="Error during file upload.",h[U]="Parsing response failed.",h[z]="File is too large.",h[O]="File type is invalid.",h[7]="Files can be uploaded only to same domain in IE 8 and IE 9.",h[8]="File is corrupted.",h[9]="Error during file loading.",h[$]="File upload cancelled";var i,ne,ae,re,oe,se,n,le,de,fe,ce="";function pe(e){ce=e,i=document.getElementsByClassName(e),Array.prototype.map.call(i,function(e){!function n(e){if(e.addEventListener("dragover",function(e){e.preventDefault(),e.stopPropagation(),ne=e.pageX,ae=e.pageY;var t=document.getElementById("filesList");ae+20>t.getBoundingClientRect().bottom&&ge(t,0,10),ae-20<t.getBoundingClientRect().top&&ge(t,0,-10)},!1),A.helpers.isMobile()){var t=e.getElementsByClassName("dot");t[0].addEventListener("touchmove",function(e){e.preventDefault(),e.stopPropagation();for(var t=e.target;t&&!t.classList.contains(ce);)t=t.parentElement;for(var i=document.elementFromPoint(e.targetTouches[0].clientX,e.targetTouches[0].clientY);i&&!i.classList.contains(ce);)i=i.parentElement;var n=document.getElementsByClassName("fr-hovered-over-file");Array.prototype.forEach.call(n,function(e){e.classList.remove("fr-hovered-over-file")}),i&&!t.classList.contains("fr-unchecked")&&i.classList.add("fr-hovered-over-file");var a=document.getElementById("filesList");e.targetTouches[0].clientY+5>a.getBoundingClientRect().bottom&&ge(a,0,5),e.targetTouches[0].clientY-5<a.getBoundingClientRect().top&&ge(a,0,-5)},!1)}if(e.ondrag=ue,e.ondragend=me,A.helpers.isMobile()){var i=e.getElementsByClassName("dot");i[0].addEventListener("touchmove",ue,!1),i[0].addEventListener("touchend",me,!1)}}(e)})}function ge(e,t,i){e.scrollLeft+=t,e.scrollTop+=i}function ue(e){for(A.helpers.isMobile()&&(oe=event.touches[0].clientX,se=event.touches[0].clientY),re=e.target;!re.classList.contains(ce);)re=re.parentElement;re.classList.contains(ce)&&!re.classList.contains("fr-unchecked")?A.helpers.isMobile()&&re.classList.add("drag-sort-active"):re=undefined}function me(e){var t;if(re!==undefined){var i,n;if(A.helpers.isMobile())for(i=oe,n=se,t=event.target;!t.classList.contains(ce);)t=t.parentElement;else i=event.clientX,n=event.clientY;A.helpers.isMobile()||!A.browser.safari&&!A.browser.mozilla||(i=ne,n=ae);for(var a=document.elementFromPoint(i,n);a&&!a.classList.contains(ce);)a=a.parentElement;a&&!a.classList.contains(ce)?a=undefined:a&&re!==a&&function s(e,t){var i,n,a=e.parentNode,r=t.parentNode;if(!a||!r||a.isEqualNode(t)||r.isEqualNode(e))return;for(var o=0;o<a.children.length;o++)a.children[o].isEqualNode(e)&&(i=o);for(var o=0;o<r.children.length;o++)r.children[o].isEqualNode(t)&&(n=o);a.isEqualNode(r)&&i<n&&n++;a.insertBefore(t,a.children[i]),r.insertBefore(e,r.children[n])}(re,a),A.helpers.isMobile()&&(t.classList.remove("fr-hovered-over-file"),a.classList.remove("fr-hovered-over-file"))}}function ve(e){var n=A.popups.get("filesManager.insert");n.find(".fr-upload-progress-layer").hasClass("fr-active")||n.find(".fr-upload-progress-layer").addClass("fr-active"),n.find(".fr-upload-progress").removeClass("fr-none");var t=L.get(e),i=function o(e){var t,i={weekday:"long",year:"numeric",month:"long",day:"numeric"};try{t=e.toLocaleDateString(A.opts.language?A.opts.language:undefined,i)}catch(n){t=e.toLocaleDateString(undefined,i)}return t+""}(new Date),a=D.get(e)?"":"fr-unchecked ",r="\n        <div id='fr-file-".concat(e,"' class='fr-file-list-item fr-file-").concat(e," ").concat(a,"' draggable = \"").concat(!a,'" >\n        <div class=\'fr-file-item-left\' >\n\n    \n            <div class="fr-file-item-insert-checkbox fr-files-checkbox-line">\n            ').concat(A.helpers.isMobile()?"<div id='fr-pick-".concat(e,"}' class='dot'>\n            </div>"):"",'\n            <div id="checkbox-key-').concat(e,'" class="fr-files-checkbox fr-insert-checkbox  fr-checkbox-').concat(e,'">\n            <input name="target" class="fr-insert-attr fr-checkbox-file-').concat(e,' fr-file-insert-check" data-cmd="fileInsertCheckbox"\n             data-checked="_blank" type="checkbox" id="fr-link-target-').concat(A.id,'" tabIndex="0" />\n            <span>').concat('<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="10" height="10" viewBox="0 0 32 32"><path d="M27 4l-15 15-7-7-5 5 12 12 20-20z" fill="#FFF"></path></svg>','\n            </span>\n        </div>\n                <label id="fr-label-target-').concat(A.id,"\"></label>\n            </div>\n    \n            <div class='fr-file-item-icon fr-file-item-icon-").concat(e,"' >\n                <img src='https://secure.webtoolhub.com/static/resources/icons/set112/f2afb6f7.png' alt='Image preview' class='thumbnail-padding' height='36px' width='36px' />\n            </div>\n\n            <div class='fr-file-item-description' >\n                <div class='fr-file-name fr-files-manager-tooltip'>\n                   ").concat(function s(e,t,i){null==t&&(t=100);null==i&&(i="...");return e.length>t?e.substring(0,t-i.length)+i:e}(t.name,20),'\n                      <span class="').concat(20<t.name.length?"tooltiptext":"fr-none",'">').concat(t.name,"\n                      </span>\n                 </div>\n                 <div class='fr-file-details'>\n                 <div class='fr-file-date'>").concat(i,"\n                 </div>\n \n                 <div class='fr-file-size'>\n                     ").concat(function l(e){if(0==e)return"0 Bytes";var t=Math.floor(Math.log(e)/Math.log(1024));return" | "+1*(e/Math.pow(1024,t)).toFixed(2)+" "+["Bytes","KB","MB","GB","TB"][t]}(t.size),"\n                 </div>\n                 </div>\n\n                  <div class='fr-file-error'>\n                    <h5 class='fr-file-error-h5'></h5>\n                 </div>\n             </div>\n    \n        </div>\n\n        <div class='fr-file-item-right fr-file-item-right-").concat(e,"'>")+ye(e)+"</div>\n    </div>";n.find(".fr-upload-progress-layer")[0].innerHTML=r+n.find(".fr-upload-progress-layer")[0].innerHTML,L.forEach(function d(e,t,i){D.get(t)&&n.find("input.fr-insert-attr.fr-checkbox-file-".concat(t))[0].setAttribute("checked",null)}),T.forEach(function(e){document.getElementById("fr-file-autoplay-button-"+e).checked=!0}),be(e,t),Q(),A.opts.toolbarBottom?y(!0):A.popups.setPopupDimensions(n),pe("fr-file-list-item")}function he(e){switch(e){case"application/msword":return A.icon.getFileIcon("docIcon");case"application/vnd.openxmlformats-officedocument.wordprocessingml.document":return A.icon.getFileIcon("docxIcon");case"image/gif":return A.icon.getFileIcon("gifIcon");case"image/jpeg":return A.icon.getFileIcon("jpegIcon");case"image/jpeg":return A.icon.getFileIcon("jpgIcon");case"type/text":return A.icon.getFileIcon("logIcon");case"video/quicktime":return A.icon.getFileIcon("movIcon");case"audio/mp3":case"audio/mpeg":return A.icon.getFileIcon("mp3Icon");case"video/mp4":return A.icon.getFileIcon("mp4Icon");case"audio/ogg":return A.icon.getFileIcon("oggIcon");case"video/ogg":return A.icon.getFileIcon("ogvIcon");case"application/pdf":return A.icon.getFileIcon("pdfIcon");case"image/png":return A.icon.getFileIcon("pngIcon");case"text/plain":return A.icon.getFileIcon("txtIcon");case"video/webm":return A.icon.getFileIcon("webmIcon");case"image/webp":return A.icon.getFileIcon("webpIcon");case"video/x-ms-wmv":return A.icon.getFileIcon("wmvIcon");case"application/vnd.ms-excel":return A.icon.getFileIcon("xlsIcon");case"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":return A.icon.getFileIcon("xlsxIcon");case"application/x-zip-compressed":case"application/zip":return A.icon.getFileIcon("zipIcon");default:return A.icon.getFileIcon("defaultIcon")}}function be(n,a,e){var r=A.popups.get("filesManager.insert"),t=r.find(".fr-file-item-icon-"+n).get(0);if(Ue(Le(a))&&"image/gif"!=Le(a)&&"image/webp"!=Le(a)){"a"!=t.children[0].localName&&(t.innerHTML="<a target='_blank' href=''>"+t.innerHTML+"</a>");r.find(".fr-file-item-icon-"+n).get(0).children[0].children[0];var o=new FileReader;if(null!=e&&e){var i=L.get(n);a.name=i.name,L.set(n,a)}if(o.onloadend=function(){r.find(".fr-file-item-icon-"+n).get(0).children[0].children[0].src=o.result;for(var e=atob(o.result.split(",")[1]),t=[],i=0;i<e.length;i++)t.push(e.charCodeAt(i));r.find(".fr-file-item-icon-"+n).get(0).children[0].href=window.URL.createObjectURL(new Blob([new Uint8Array(t)],{type:Le(a)})),r.find(".fr-file-item-icon-"+n).get(0).classList.add("file-item-thumbnail-hover")},a)o.readAsDataURL(a);else{var s=he(Le(a));t.innerHTML='<svg height="40px" width="40px" viewBox="0 0 55 5" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n        '.concat(s.path,"\n        </svg>")}}else{var l=he(Le(a));t.innerHTML='<svg height="40px" width="40px" viewBox="0 0 55 55" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">\n      '.concat(l.path,"\n      </svg>")}}function ye(e){var t="";if(Oe(Le(L.get(e)))){var i="fr-files-checkbox",n="";Pe(Le(L.get(e)))||(i="fr-checkbox-disabled",n="disabled");t='\n      <div class="fr-files-checkbox-line align-autoplay">\n      <div id="checkbox-key-'.concat(e,'" class="').concat(i," fr-autoplay-checkbox  fr-checkbox-").concat(e,'">  \n                   \n      <input type="checkbox" id="fr-file-autoplay-button-').concat(e,'" class="fr-file-button-').concat(e,' fr-file-autoplay-button" data-title="Edit" data-param1="').concat(e,'" role="button" ').concat(n,"/>\n\n      <span>").concat('<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="10" height="10" viewBox="0 0 32 32"><path d="M27 4l-15 15-7-7-5 5 12 12 20-20z" fill="#FFF"></path></svg>'," </span>\n      </div>      \n      <label  class='fr-autoplay-checkbox-label'>Autoplay </label>\n      </div>")}var a="application/msword",r="application/vnd.openxmlformats-officedocument.wordprocessingml.document",o="";return!$e(Le(L.get(e)))&&Pe(Le(L.get(e)))||(s="fr-disabled"),Oe(Le(L.get(e)))&&(s="fr-disabled"),ze(Le(L.get(e)))&&(s="fr-disabled",Le(L.get(e))!=a&&Le(L.get(e))!=r||A.opts.googleOptions&&!A.helpers.isMobile()&&A.opts.googleOptions.API_KEY&&A.opts.googleOptions.CLIENT_ID&&(s=""),"text/plain"!=Le(L.get(e))&&Le(L.get(e))!=a&&"application/pdf"!=Le(L.get(e))&&Le(L.get(e))!=r&&"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"!=Le(L.get(e))&&"application/vnd.ms-excel"!=Le(L.get(e))&&"type/text"!=Le(L.get(e))||(o="")),"video/url"===Le(L.get(e))&&(s="fr-disabled"),t+='<div class=\'fr-file-item-action-buttons\' >\n                <button type="button" id="fr-file-insert-button-'.concat(e,'" class=" fr-doc-edit-').concat(e," fr-img-icon fr-btn fr-command fr-submit fr-file-action-icons \n                fr-file-button-").concat(e," fr-file-insert-button-").concat(e,' fr-file-insert-button" data-cmd="imageInsertByUpload" data-title="Insert" data-param1="').concat(e,'" tabIndex="2" role="button">\n                <svg style=\'margin:0px !important; opacity:0.9\' class = "fr-svg" focusable="false" width="16px" height="16px" viewBox = "-5 0 28 28" xlmns = "http://w3.org/200/svg"><path d = \'M 9.25 12 L 6.75 12 C 6.335938 12 6 11.664062 6 11.25 L 6 6 L 3.257812 6 C 2.703125 6 2.425781 5.328125 2.820312 4.933594 L 7.570312 0.179688 C 7.804688 -0.0546875 8.191406 -0.0546875 8.425781 0.179688 L 13.179688 4.933594 C 13.574219 5.328125 13.296875 6 12.742188 6 L 10 6 L 10 11.25 C 10 11.664062 9.664062 12 9.25 12 Z M 16 11.75 L 16 15.25 C 16 15.664062 15.664062 16 15.25 16 L 0.75 16 C 0.335938 16 0 15.664062 0 15.25 L 0 11.75 C 0 11.335938 0.335938 11 0.75 11 L 5 11 L 5 11.25 C 5 12.214844 5.785156 13 6.75 13 L 9.25 13 C 10.214844 13 11 12.214844 11 11.25 L 11 11 L 15.25 11 C 15.664062 11 16 11.335938 16 11.75 Z M 12.125 14.5 C 12.125 14.15625 11.84375 13.875 11.5 13.875 C 11.15625 13.875 10.875 14.15625 10.875 14.5 C 10.875 14.84375 11.15625 15.125 11.5 15.125 C 11.84375 15.125 12.125 14.84375 12.125 14.5 Z M 14.125 14.5 C 14.125 14.15625 13.84375 13.875 13.5 13.875 C 13.15625 13.875 12.875 14.15625 12.875 14.5 C 12.875 14.84375 13.15625 15.125 13.5 15.125 C 13.84375 15.125 14.125 14.84375 14.125 14.5 Z M 14.125 14.5 \'></path></svg>\n                </button>\n\n                <button type="button" id="fr-file-edit-button-').concat(e,'" class=" fr-doc-edit-').concat(e," ").concat(s," fr-img-icon fr-btn fr-command fr-submit \n                fr-file-action-icons fr-file-edit-button-").concat(e," fr-file-button-").concat(e,' fr-file-edit-button" data-cmd="editImage" data-title="Edit" data-param1="').concat(e,'" role="button">\n                <svg style=\'margin:0px !important; opacity:0.9\' class = "fr-svg" focusable="false" width="16px" height="16px" viewBox = "0 4 25 25" xlmns = "http://w3.org/200/svg"><path d = \'M17,11.2L12.8,7L5,14.8V19h4.2L17,11.2z M7,16.8v-1.5l5.6-5.6l1.4,1.5l-5.6,5.6H7z M13.5,6.3l0.7-0.7c0.8-0.8,2.1-0.8,2.8,0  c0,0,0,0,0,0L18.4,7c0.8,0.8,0.8,2,0,2.8l-0.7,0.7L13.5,6.3z\'></path></svg>\n                </button>\n                \n                <span class="fr-file-view-').concat(e,'"><button type="button" id="fr-file-view-button-').concat(e,'" class=" fr-doc-edit-').concat(e," ").concat(o," fr-img-icon fr-btn fr-command fr-submit fr-file-action-icons \n                fr-file-view-button-").concat(e,' fr-file-view-button" data-cmd="viewImage" data-title="View" data-param1="').concat(e,'" tabIndex="2" role="button">\n                <svg style=\'margin:0px !important; opacity:0.9\' class = "fr-svg" focusable="false" width="16px" height="16px" viewBox = "15 19 21 21" xlmns = "http://w3.org/200/svg"> <path style="fill:none;stroke-width:0.9077;stroke-linecap:round;stroke-linejoin:round;stroke:rgb(0%,0%,0%);stroke-opacity:1;stroke-miterlimit:10;" d="M 19.086094 16.541466 C 16.185625 16.541466 14.318281 19.447115 14.318281 19.447115 L 14.318281 19.555288 C 14.318281 19.555288 16.176719 22.475962 19.077187 22.475962 C 21.977656 22.475962 23.847969 19.576322 23.847969 19.576322 L 23.847969 19.465144 C 23.847969 19.465144 21.989531 16.541466 19.086094 16.541466 Z M 19.07125 21.024639 C 18.248906 21.024639 17.583906 20.357572 17.583906 19.53726 C 17.583906 18.716947 18.248906 18.04988 19.07125 18.04988 C 19.890625 18.04988 20.555625 18.716947 20.555625 19.53726 C 20.555625 20.357572 19.890625 21.024639 19.07125 21.024639 Z M 19.07125 21.024639 " transform="matrix(1.315789,0,0,1.3,0,0)"/></svg></button></span>\n\n                <button type="button" id="fr-file-delete-button-').concat(e,'" class=" fr-doc-edit-').concat(e," fr-img-icon fr-btn fr-command fr-submit fr-file-action-icons\n                fr-file-button-").concat(e,' fr-file-delete-button" data-cmd="deleteImage" data-title="Delete" data-param1="').concat(e,'" role="button">\n                <svg style=\'margin:0px !important; opacity:0.9\' class = "fr-svg" focusable="false" width="16px" height="16px" viewBox = "-2 3 30 30" xlmns = "http://w3.org/200/svg"><path d = \'M15,10v8H9v-8H15 M14,4H9.9l-1,1H6v2h12V5h-3L14,4z M17,8H7v10c0,1.1,0.9,2,2,2h6c1.1,0,2-0.9,2-2V8z\'></path></svg>\n                </button>\n                 \n            </div>\n            <div id="user_area-').concat(e,"\" style=\"display: none;\">\n            \n              <div id=\"file_container\"></div>\n\n              <div style='display:block;text-align: center; margin-left:50%; id='edit-file-loader' class='fr-file-loader'></div>\n\n          </div> \n            ")}function we(e,t,i){var n=A.popups.get("filesManager.insert");if(!i&&e<=100){n.find(".fr-checkbox-file-"+t).get(0).disabled=!0,n.find(".fr-checkbox-"+t).get(0).classList.remove("fr-files-checkbox"),n.find(".fr-checkbox-"+t).get(0).classList.add("fr-checkbox-disabled");var a=n.find(".fr-file-progress-circle-"+t),r=n.find(".fr-file-upload-percent-"+t);return 50<e?a.get(0).setAttribute("class","fr-file-progress-circle-"+t+" progress-circle p"+Math.floor(e)+" over50"):a.get(0).setAttribute("class","fr-file-progress-circle-"+t+" progress-circle p"+Math.floor(e)),r.get(0).innerHTML=Math.floor(e)+"%",void xe(t,e,i)}i&&(n.find(".fr-checkbox-file-"+t).get(0).disabled=!1,n.find(".fr-checkbox-"+t).get(0).classList.remove("fr-checkbox-disabled"),n.find(".fr-checkbox-"+t).get(0).classList.add("fr-files-checkbox"),n.find(".fr-file-item-right-"+t).get(0).innerHTML=ye(t),xe(t,100,i))}function xe(e,t,i){var n=A.popups.get("filesManager.insert");n.find(".fr-progress-bar").removeClass("fr-none").addClass("fr-display-block"),n.find(".fr-upload-progress").hasClass("fr-height-set")&&A.popups.setFileListHeight(n);var a=0;k.set(e,t),k.forEach(function(e,t){a+=e}),a/=k.size,100==t&&i&&r++,n.find('.fr-command[data-cmd="filesUpload"]').addClass("fr-disabled"),n.find('.fr-command[data-cmd="filesByURL"]').addClass("fr-disabled"),n.find('.fr-command[data-cmd="filesEmbed"]').addClass("fr-disabled"),n.find(".fr-progress-bar").get(0).style.width=a+"%",r==k.size&&(n.find(".fr-progress-bar").removeClass("fr-display-block").addClass("fr-none"),k=new Map,r=0,n.find('.fr-command[data-cmd="filesUpload"]').removeClass("fr-disabled"),n.find('.fr-command[data-cmd="filesByURL"]').removeClass("fr-disabled"),n.find('.fr-command[data-cmd="filesEmbed"]').removeClass("fr-disabled"))}function Me(i,n){Q(),A.popups.get("filesManager.insert").find(".fr-upload-progress-layer").addClass("fr-active"),i.forEach(function(e,t){Ue(Le(e))&&A.opts.imageUploadRemoteUrls&&A.opts.imageCORSProxy&&A.opts.imageUpload?Te(e,i,x,n[t]):_.set(n[t],e)})}function Ee(e){e&&e.get&&function i(e){if("false"==I(this).parents("[contenteditable]").not(".fr-element").not(".fr-img-caption").not("body").first().attr("contenteditable"))return!0;if(e&&"touchend"==e.type&&fe)return!0;if(e&&A.edit.isDisabled())return e.stopPropagation(),e.preventDefault(),!1;for(var t=0;t<It.INSTANCES.length;t++)It.INSTANCES[t]!=A&&It.INSTANCES[t].events.trigger("image.hideResizer");A.toolbar.disable(),e&&(e.stopPropagation(),e.preventDefault());A.helpers.isMobile()&&(A.events.disableBlur(),A.$el.blur(),A.events.enableBlur());A.opts.iframe&&A.size.syncIframe();x=I(this),N(),A.browser.msie?(A.popups.areVisible()&&A.events.disableBlur(),A.win.getSelection&&(A.win.getSelection().removeAllRanges(),A.win.getSelection().addRange(A.doc.createRange()))):A.selection.clear();A.helpers.isIOS()&&(A.events.disableBlur(),A.$el.blur());A.button.bulkRefresh(),A.events.trigger("video.hideResizer")}.call(e.get(0))}function ke(){var e=I(this);e.removeClass("fr-uploading"),e.next().is("br")&&e.next().remove(),(0==a.length||0<a.length&&a.length==g)&&(c=e),"VIDEO"==e.get(0).tagName||"AUDIO"==e.get(0).tagName?A.selection.setAfter(e.parent()):A.selection.setAfter(e),A.undo.saveStep(),A.events.trigger("filesManager.loaded",[e]),Ce(a)}function Ae(){var e,t=Array.prototype.slice.call(A.el.querySelectorAll("video, .fr-video > *")),i=[];for(e=0;e<t.length;e++)i.push(t[e].getAttribute("src")),I(t[e]).toggleClass("fr-draggable",A.opts.videoMove),""===t[e].getAttribute("class")&&t[e].removeAttribute("class"),""===t[e].getAttribute("style")&&t[e].removeAttribute("style");if(n)for(e=0;e<n.length;e++)i.indexOf(n[e].getAttribute("src"))<0&&A.events.trigger("video.removed",[I(n[e])]);n=t}function Ce(e){if(null!=e){if(0==e.length)return void(null!=c&&("VIDEO"==c.get(0).tagName?A.video._editVideo(c.parent()):"IMG"==c.get(0).tagName?A.image.edit(c):c.trigger("click"),A.toolbar.disable()));_e(e.shift(),e)}}function Ie(e){var t=!1;if(Oe(Le(_.get(e))))A.trimVideoPlugin.trimVideo(L.get(e),e,L),t=!0;else if(Ue(Le(_.get(e)))){var i=_.get(e).link,n=A.o_doc.createElement("img");n.src=i,x=n,o=e,A.imageTUI.launch(A,!1,e),t=!0}else if(ze(Le(_.get(e)))){var a={apiKey:A.opts.googleOptions.API_KEY,clientId:A.opts.googleOptions.CLIENT_ID,authorizeButton:"authorize_button-".concat(e),signoutButton:"signout_button",userArea:"user_area-".concat(e),fileInput:"file_input",fileIndex:e,file:L.get(e),fileContainer:"file_container",loadingText:"File is being uploaded...",events:{onInvalidFile:function(e){},onError:function(e){}}};de=function f(p){var r,e=["https://www.googleapis.com/discovery/v1/apis/drive/v2/rest"],g="id,title,mimeType,userPermission,editable,copyable,shared,fileSize",u="-------314159265358979323846",m="\r\n--"+u+"\r\n",v="\r\n--"+u+"--",t=(document.getElementById(p.authorizeButton),document.getElementById(p.userArea));p.events||(p.events={});function i(){gapi.client.init({apiKey:p.apiKey,clientId:p.clientId,discoveryDocs:e,scope:"https://www.googleapis.com/auth/drive https://www.googleapis.com/auth/drive.file https://www.googleapis.com/auth/drive.appdata"}).then(function(){gapi.auth2.getAuthInstance().isSignedIn.listen(n),n(gapi.auth2.getAuthInstance().isSignedIn.get()),function t(e){!gapi.auth2.getAuthInstance().isSignedIn.get()||gapi.auth.getToken()!==undefined&&gapi.auth.getToken().access_token===undefined?Promise.resolve(gapi.auth2.getAuthInstance().signIn()).then(function(){o()}):o()}()},function(e){p.events.onError(e)})}function n(e){e&&(t.style.display="block")}function a(e){var t=gapi.auth.getToken().access_token,i=r,n="https://docs.google.com/feeds/download/documents/export/Export?id="+i+"&format=docx&access_token="+t,a=new XMLHttpRequest;a.open("get",n),a.responseType="arraybuffer",a.onload=function(){var e=new Blob([new Uint8Array(this.response)],{type:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"}),t=L.get(p.fileIndex);e.name=t.name,e.lastModified=t.lastModified,e.lastModifiedDate=t.lastModifiedDate,L.set(p.fileIndex,e),A.filesManager.upload(e,_,x,p.fileIndex),l()},a.send()}function o(e){!function n(e){for(var t=document.getElementsByClassName("fr-doc-edit-".concat(e)),i=0;i<t.length;i++)t[i].setAttribute("disabled",!0),t[i].classList.add("fr-disabled")}(p.fileIndex);var t=p.file;t?function i(d,f){var c=new FileReader;c.readAsArrayBuffer(d),c.onload=function(e){for(var t={title:d.name,mimeType:"application/vnd.google-apps.document"},i="",n=new Uint8Array(c.result),a=n.byteLength,r=0;r<a;r++)i+=String.fromCharCode(n[r]);var o=btoa(i),s=m+"Content-Type: application/json; charset=UTF-8\r\n\r\n"+JSON.stringify(t)+m+"Content-Type: application/octet-stream\r\nContent-Transfer-Encoding: base64\r\n\r\n"+o+v,l=gapi.client.request({path:"/upload/drive/v2/files",method:"POST",params:{uploadType:"multipart",fields:g},headers:{"Content-Type":'multipart/related; boundary="'+u+'"',"Content-Length":s.Length},body:s});f||(f=function(e){}),l.execute(function(e,t){e.error?p.events.onError(e.error):f(e)})}}(t,s):p.events.onInvalidFile("File is not selected")}function s(e){r=e.id;var t="https://docs.google.com/document/d/"+e.id+"/edit",i=A.o_doc.body,n=A.o_doc.createElement("div");n.setAttribute("id","editDocContainer"),n.style.cssText="position: fixed; top: 0;left: 0;padding: 0;width: 100%;height: 100%;background: rgba(255,255,255,1);z-index: 9998;display:block",n.innerHTML='<div style="margin-top:25px; text-align:center"><label>Sign Out : </label><input type="checkbox" id ="markSignOut" role="button"/>  <button id="signout_button"  class="fr-trim-button" >Save </button> <button id="cancel_file_edit" class="fr-trim-button">Cancel</button></div>  <iframe title="Edit your file" frameBorder="0" width="100%" height="700px" src="'+t+'"></iframe>',i.appendChild(n),document.getElementById("signout_button").onclick=a,document.getElementById("cancel_file_edit").onclick=l}function l(){document.getElementById("markSignOut").checked&&gapi.auth2.getAuthInstance().signOut().then(function(){gapi.auth.getToken()&&(gapi.auth.getToken().access_token=undefined)});var e=document.getElementById("editDocContainer");e.parentNode.removeChild(e),document.getElementById("user_area-".concat(p.fileIndex))&&(document.getElementById("user_area-".concat(p.fileIndex)).style.display="none"),function n(e){for(var t=document.getElementsByClassName("fr-doc-edit-".concat(e)),i=0;i<t.length;i++)t[i].removeAttribute("disabled"),t[i].classList.remove("fr-disabled")}(p.fileIndex)}p.events.onInvalidFile=p.events.onInvalidFile||function(e){},p.events.onError=p.events.onError||function(e){};var d={};return d.handleClientLoad=function(){gapi.load("client:auth2",i)},d}(a),function r(e,t){var n=function n(e,t){var i=document.createElement("script");i.src=e,i.onload=function(){this.onload=function(){},de.handleClientLoad()},i.onreadystatechange=function(){"complete"===this.readyState&&this.onload()},(document.getElementsByTagName("head")[0]||document.body).appendChild(i)};!function i(){0!=e.length?n(e.shift(),i):t&&t()}()}(["https://apis.google.com/js/api.js"],function(){})}t&&(p=!0)}function Le(e){var i;if(""!=e.type)return e.type;if(e.name&&e.name.endsWith(".msg"))return i="application/vnd.ms-outlook";var n=/(?:\.([^.]+))?$/.exec(e.name)[1];return[[".doc","application/msword"],[".docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],[".gif","image/gif"],[".jpeg","image/jpeg"],[".jpg","image/jpeg"],[".txt","text/plain"],[".log","type/text"],[".mov","video/quicktime"],[".mp3","audio/mpeg"],[".mp4","video/mp4"],[".ogg","audio/ogg"],[".ogv","video/ogg"],[".pdf","application/pdf"],[".png","image/png"],[".webm","video/webm"],[".webp","image/webp"],[".wmv","video/x-ms-wmv"],[".xls","application/vnd.ms-excel"],[".xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],[".zip","application/x-zip-compressed"]].forEach(function(e,t){e[0]==".".concat(n)&&(i=e[1])}),i}function _e(s,e){if(ze(Le(_.get(s)))||!Pe(Le(_.get(s)))){var t=_.get(s).link,i=_.get(s).text;!i&&L.get(s)&&L.get(s).name&&(i=L.get(s).name);var n=_.get(s).response;A.edit.on(),A.events.focus(!0),A.selection.restore(),A.opts.fileUseSelectedText&&A.selection.text().length&&(i=A.selection.text()),A.html.insert('<a href="'.concat(t,'" target="_blank" id="fr-inserted-file" class="fr-file">').concat(i,"</a>"));var a=A.$el.find("#fr-inserted-file");a.removeAttr("id"),A.undo.saveStep(),function M(){var e,t=Array.prototype.slice.call(A.el.querySelectorAll("a.fr-file")),i=[];for(e=0;e<t.length;e++)i.push(t[e].getAttribute("href"));if(le)for(e=0;e<le.length;e++)i.indexOf(le[e].getAttribute("href"))<0&&A.events.trigger("file.unlink",[le[e]]);le=t}(),A.selection.clear(),A.selection.setAfter(a),A.events.trigger("file.inserted",[a,n]),Ce(e)}if(Ue(Le(_.get(s)))&&Pe(Le(_.get(s)))){var o=_.get(s).link,r=_.get(s).sanitize,l=_.get(s).data,d=_.get(s).$existing_img,f=_.get(s).response;d&&"string"==typeof d&&(d=A.$(d)),A.edit.off(),ee(A.language.translate("Loading image")),r&&(o=A.helpers.sanitizeURL(o));var c=new Image;c.onload=function(){var e,t;if(d){A.undo.canDo()||d.hasClass("fr-uploading")||A.undo.saveStep();var i=d.data("fr-old-src");d.data("fr-image-pasted")&&(i=null),A.$wp?((e=d.clone().removeData("fr-old-src").removeClass("fr-uploading").removeAttr("data-fr-image-pasted")).off("load"),i&&d.attr("src",i),d.replaceWith(e)):e=d;for(var n=e.get(0).attributes,a=0;a<n.length;a++){var r=n[a];0===r.nodeName.indexOf("data-")&&e.removeAttr(r.nodeName)}if(void 0!==l)for(t in l)l.hasOwnProperty(t)&&"link"!=t&&e.attr("data-".concat(t),l[t]);e.on("load",ke),e.attr("src",o),A.edit.on(),A.undo.saveStep(),A.events.disableBlur(),A.$el.blur(),A.events.trigger(i?"image.replaced":"image.inserted",[e,f])}else e=function s(e,t,i){var n,a=I(document.createElement("img")).attr("src",e);if(t&&void 0!==t)for(n in t)t.hasOwnProperty(n)&&"link"!=n&&(" data-".concat(n,'="').concat(t[n],'"'),a.attr("data-".concat(n),t[n]));var r=A.opts.imageDefaultWidth;r&&"auto"!=r&&(r=A.opts.imageResizeWithPercent?"100%":"".concat(r,"px"));a.attr("style",r?"width: ".concat(r,";"):""),ot(a,A.opts.imageDefaultDisplay,A.opts.imageDefaultAlign),a.on("load",i),a.on("error",i),A.edit.on(),A.events.focus(!0),A.selection.restore(),A.undo.saveStep(),A.opts.imageSplitHTML?A.markers.split():A.markers.insert();A.html.wrap();var o=A.$el.find(".fr-marker");o.length?(o.parent().is("hr")&&o.parent().after(o),A.node.isLastSibling(o)&&o.parent().hasClass("fr-deletable")&&o.insertAfter(o.parent()),o.replaceWith(a)):A.$el.append(a);return a}(o,l,ke),A.undo.saveStep(),A.events.disableBlur(),A.$el.blur(),A.events.trigger("image.inserted",[e,f])},c.onerror=function(){X(B,null,null,s),Ce(e)},c.src=o}if((Oe(Le(_.get(s)))||$e(Le(_.get(s))))&&Pe(Le(_.get(s))))if(C=s,"video/url"==Le(_.get(s))){var p=!1;if(document.getElementById("fr-file-autoplay-button-"+s)!==undefined&&(p=document.getElementById("fr-file-autoplay-button-"+s).checked),p&&_.get(s)!==undefined&&-1<_.get(s).video.indexOf("iframe")&&_.get(s).video.indexOf("autoplay=1")<0){var g=_.get(s).video.substring(_.get(s).video.indexOf("src")+3),u="&";(g=(g=g.substring(g.indexOf('"')+1)).substring(0,g.indexOf('"'))).indexOf("?")<0&&(u="?"),_.get(s).video=_.get(s).video.replace(g,g+=u+"autoplay=1&mute=1")}else!p&&_.get(s).video.indexOf(!1)&&(-1<_.get(s).video.indexOf("&autoplay=1")&&(_.get(s).video=_.get(s).video.replace("&autoplay=1","")),-1<_.get(s).video.indexOf("?autoplay=1")&&(_.get(s).video=_.get(s).video.replace("?autoplay=1","")));A.events.focus(!0),A.selection.restore(),A.html.insert('<span contenteditable="false" draggable="true" class="fr-jiv fr-video fr-deletable">'.concat(_.get(s).video,"</span>"),!1,A.opts.videoSplitHTML),A.popups.hide("filesManager.insert");var m=A.$el.find(".fr-jiv");m.removeClass("fr-jiv"),m.toggleClass("fr-rv",A.opts.videoResponsive),function E(e,t,i){!A.opts.htmlUntouched&&A.opts.useClasses?(e.removeClass("fr-fvl fr-fvr fr-dvb fr-dvi"),e.addClass("fr-fv".concat(i[0]," fr-dv").concat(t[0]))):"inline"==t?(e.css({display:"inline-block"}),"center"==i?e.css({"float":"none"}):"left"==i?e.css({"float":"left"}):e.css({"float":"right"})):(e.css({display:"block",clear:"both"}),"left"==i?e.css({textAlign:"left"}):"right"==i?e.css({textAlign:"right"}):e.css({textAlign:"center"}))}(m,A.opts.videoDefaultDisplay,A.opts.videoDefaultAlign),m.toggleClass("fr-draggable",A.opts.videoMove),A.events.trigger("video.inserted",[m]),ke.call(m)}else{var v=_.get(s).link,h=_.get(s).sanitize,b=_.get(s).data,y=_.get(s).$existing_img,w=_.get(s).response;A.edit.off(),h&&(v=A.helpers.sanitizeURL(v)),function k(){var e,t;if(y){A.undo.canDo()||y.find("video").hasClass("fr-uploading")||A.undo.saveStep();var i=y.find("video").data("fr-old-src"),n=y.data("fr-replaced");y.data("fr-replaced",!1),A.$wp?((e=y.clone(!0)).find("video").removeData("fr-old-src").removeClass("fr-uploading"),e.find("video").off("canplay"),i&&y.find("video").attr("src",i),y.replaceWith(e)):e=y;for(var a=e.find("video").get(0).attributes,r=0;r<a.length;r++){var o=a[r];0===o.nodeName.indexOf("data-")&&e.find("video").removeAttr(o.nodeName)}if(void 0!==b)for(t in b)b.hasOwnProperty(t)&&"link"!=t&&e.find("video").attr("data-".concat(t),b[t]);e.find("video").on("canplay",ke),e.find("video").attr("src",v),A.edit.on(),Ae(),A.undo.saveStep(),A.$el.blur(),A.events.trigger(n?"video.replaced":"video.inserted",[e,w])}else e=function g(e,t,i,n,a){var r,o="";if(t&&void 0!==t)for(r in t)t.hasOwnProperty(r)&&"link"!=r&&(o+=" data-".concat(r,'="').concat(t[r],'"'));var s,l=A.opts.videoDefaultWidth;l&&"auto"!=l&&(l="".concat(l,"px"));if($e(n))s=I(document.createElement("span")).attr("contenteditable","false").attr("draggable","true").attr("class","fr-video fr-dv"+A.opts.videoDefaultDisplay[0]+("center"!=A.opts.videoDefaultAlign?" fr-fv"+A.opts.videoDefaultAlign[0]:"")).html('<audio src="'+e+'" '+o+" controls>"+A.language.translate("Your browser does not support HTML5 video.")+"</audio>");else{var d="",f=document.getElementById("fr-file-autoplay-button-"+a).checked;f&&(d="autoplay"),s=I(document.createElement("span")).attr("contenteditable","false").attr("draggable","true").attr("class","fr-video fr-dv"+A.opts.videoDefaultDisplay[0]+("center"!=A.opts.videoDefaultAlign?" fr-fv"+A.opts.videoDefaultAlign[0]:"")).html('<video src="'+e+'" '+o+(l?' style="width: '+l+';" ':"")+d+"  controls>"+A.language.translate("Your browser does not support HTML5 video.")+"</video>")}s.toggleClass("fr-draggable",A.opts.videoMove),A.edit.on(),A.events.focus(!0),A.selection.restore(),A.undo.saveStep(),A.opts.videoSplitHTML?A.markers.split():A.markers.insert();A.html.wrap();var c=A.$el.find(".fr-marker");A.node.isLastSibling(c)&&c.parent().hasClass("fr-deletable")&&c.insertAfter(c.parent());c.replaceWith(s);var p="";p=$e(n)?"audio":"video",s.find(p).get(0).readyState>s.find(p).get(0).HAVE_FUTURE_DATA||A.helpers.isIOS()?i.call(s.find(p).get(0)):(s.find(p).on("canplaythrough load",i),s.find(p).on("error",i));return s}(v,b,ke,Le(_.get(s)),s),Ae(),A.undo.saveStep(),A.events.trigger("video.inserted",[e,w])}()}A.popups.hide("filesManager.insert"),D["delete"](s);var x=A.popups.get("filesManager.insert");x.find("input.fr-insert-attr.fr-checkbox-file-".concat(s))[0].checked=!1,x.find(".fr-file-"+s).get(0).classList.add("fr-unchecked"),j(),document.getElementById("fr-file-autoplay-button-"+s)&&(document.getElementById("fr-file-autoplay-button-"+s).checked=!1),T=T.filter(function(e){return e!=s})}function De(e,t){try{if(!1===A.events.trigger("filesManager.uploaded",[e],!0))return A.edit.on(),!1;var i=JSON.parse(e);return i.link?i:(X(v,e,null,t),!1)}catch(n){return X(U,e,null,t),!1}}function Se(e,t){try{var i=I(e).find("Location").text(),n=I(e).find("Key").text();return!1===A.events.trigger("filesManager.uploadedToS3",[i,n,e],!0)?(A.edit.on(),!1):i}catch(a){return X(U,e,null,t),!1}}function Te(e,t,i,n){if(-1<P.indexOf(Le(e))||!Le(e))return X(O,null,null,n),!1;if(!1===A.events.trigger("filesManager.beforeUpload",[t]))return!1;if(!(null!==A.opts.filesManagerUploadURL&&A.opts.filesManagerUploadURL!=M||A.opts.filesManagerUploadToS3||A.opts.filesManagerUploadToAzure))return function y(s,l,d){var f=new FileReader;f.onload=function(){var e=f.result;if(f.result.indexOf("svg+xml")<0){for(var t=atob(f.result.split(",")[1]),i=[],n=0;n<t.length;n++)i.push(t.charCodeAt(n));if(e=window.URL.createObjectURL(new Blob([new Uint8Array(i)],{type:Le(l)})),Ue(Le(l))){var a={link:e,sanitize:!1,data:null,$existing_img:d,response:null,type:Le(l)};_.set(s,a)}if(ze(Le(l))){var r={link:e,text:l.name,response:null,type:Le(l)};_.set(s,r)}if(Oe(Le(l))||$e(Le(l))){var o={link:e,sanitize:!1,data:null,$existing_img:d,type:Le(l)};_.set(s,o)}}},f.readAsDataURL(l)}(n,e),!1;if(Ue(Le(e))&&(e.name||(e.name=(new Date).getTime()+"."+(Le(e)||"image/jpeg").replace(/image\//g,""))),e.size>A.opts.filesManagerMaxSize)return X(z,null,null,n),!1;if(A.opts.filesManagerAllowedTypes.indexOf("*")<0&&A.opts.filesManagerAllowedTypes.indexOf(Le(e))<0)return X(O,null,null,n),!1;var a;if(function w(e){isNaN(e)||(A.popups.get("filesManager.insert").find(".fr-file-item-right-"+e).get(0).innerHTML='<div class=\'fr-file-item-action-buttons\' >\n    <button type="button" id="fr-file-cancel-upload-button-'.concat(e,'" class="fr-img-icon fr-btn fr-command fr-submit fr-file-action-icons \n    fr-file-button-').concat(e,' fr-file-cancel-upload-button" data-cmd="cancelUpload" data-title="Cancel" data-param1="').concat(e,'" role="button">\n    <svg style=\'margin:0px !important; opacity:0.9\' class = "fr-svg" focusable="false" width="16px" height="16px" viewBox = "-2 3 30 30" xlmns = "http://w3.org/200/svg"><path d = \'M13.4,12l5.6,5.6L17.6,19L12,13.4L6.4,19L5,17.6l5.6-5.6L5,6.4L6.4,5l5.6,5.6L17.6,5L19,6.4L13.4,12z\'></path></svg>\n    </button>\n\n    <button type="button" id="fr-upload-delete-button-').concat(e,'" class="fr-img-icon fr-btn fr-command fr-submit fr-file-action-icons \n    fr-file-button-').concat(e,' fr-upload-delete-button" data-cmd="deleteUpload" data-title="Delete" data-param1="').concat(e,'" role="button">\n    <svg style=\'margin:0px !important; opacity:0.9\' class = "fr-svg" focusable="false" width="16px" height="16px" viewBox = "-2 3 30 30" xlmns = "http://w3.org/200/svg"><path d = \'M15,10v8H9v-8H15 M14,4H9.9l-1,1H6v2h12V5h-3L14,4z M17,8H7v10c0,1.1,0.9,2,2,2h6c1.1,0,2-0.9,2-2V8z\'></path></svg>\n    </button>\n\n    <div class=\'progress-circle p0 fr-file-progress-circle-').concat(e,"'>\n                  <span class='fr-file-upload-percent-").concat(e," fr-file-upload-percent'>0%</span>\n                  <div class='left-half-clipper'>\n                    <div class='first50-bar'></div>\n                    <div class='value-bar'></div>\n                  </div>\n                </div>\n            </div>"),k.set(e,0))}(n),A.drag_support.formdata&&(a=A.drag_support.formdata?new FormData:null),a){var r;if(!1!==A.opts.filesManagerUploadToS3)for(r in a.append("key",A.opts.filesManagerUploadToS3.keyStart+(new Date).getTime()+"-"+(e.name||"untitled")),a.append("success_action_status","201"),a.append("X-Requested-With","xhr"),a.append("Content-Type",Le(e)),A.opts.filesManagerUploadToS3.params)A.opts.filesManagerUploadToS3.params.hasOwnProperty(r)&&a.append(r,A.opts.filesManagerUploadToS3.params[r]);for(r in A.opts.filesManagerUploadParams)A.opts.filesManagerUploadParams.hasOwnProperty(r)&&a.append(r,A.opts.filesManagerUploadParams[r]);a.append(A.opts.filesManagerUploadParam,e,e.name);var o,s,l=A.opts.filesManagerUploadURL;A.opts.filesManagerUploadToS3&&(l=A.opts.filesManagerUploadToS3.uploadURL?A.opts.filesManagerUploadToS3.uploadURL:"https://".concat(A.opts.filesManagerUploadToS3.region,".amazonaws.com/").concat(A.opts.filesManagerUploadToS3.bucket)),A.opts.filesManagerUploadToAzure&&(l=A.opts.filesManagerUploadToAzure.uploadURL?"".concat(A.opts.filesManagerUploadToAzure.uploadURL,"/").concat(e.name):encodeURI("https://".concat(A.opts.filesManagerUploadToAzure.account,".blob.core.windows.net/").concat(A.opts.filesManagerUploadToAzure.container,"/").concat(e.name)),o=l,A.opts.filesManagerUploadToAzure.SASToken&&(l+=A.opts.filesManagerUploadToAzure.SASToken),A.opts.filesManagerUploadMethod="PUT");var d=A.core.getXHR(l,A.opts.filesManagerUploadMethod);if(A.opts.filesManagerUploadToAzure){var f=(new Date).toUTCString();if(!A.opts.filesManagerUploadToAzure.SASToken&&A.opts.filesManagerUploadToAzure.accessKey){var c=A.opts.filesManagerUploadToAzure.account,p=A.opts.filesManagerUploadToAzure.container;if(A.opts.filesManagerUploadToAzure.uploadURL){var g=A.opts.filesManagerUploadToAzure.uploadURL.split("/");p=g.pop(),c=g.pop().split(".")[0]}var u="x-ms-blob-type:BlockBlob\nx-ms-date:".concat(f,"\nx-ms-version:2019-07-07"),m=encodeURI("/"+c+"/"+p+"/"+e.name),v=A.opts.filesManagerUploadMethod+"\n\n\n"+e.size+"\n\n"+Le(e)+"\n\n\n\n\n\n\n"+u+"\n"+m,h=A.cryptoJSPlugin.cryptoJS.HmacSHA256(v,A.cryptoJSPlugin.cryptoJS.enc.Base64.parse(A.opts.filesManagerUploadToAzure.accessKey)).toString(A.cryptoJSPlugin.cryptoJS.enc.Base64),b="SharedKey "+c+":"+h;s=h,d.setRequestHeader("Authorization",b)}for(r in d.setRequestHeader("x-ms-version","2019-07-07"),d.setRequestHeader("x-ms-date",f),d.setRequestHeader("Content-Type",Le(e)),d.setRequestHeader("x-ms-blob-type","BlockBlob"),A.opts.filesManagerUploadParams)A.opts.filesManagerUploadParams.hasOwnProperty(r)&&d.setRequestHeader(r,A.opts.filesManagerUploadParams[r]);for(r in A.opts.filesManagerUploadToAzure.params)A.opts.filesManagerUploadToAzure.params.hasOwnProperty(r)&&d.setRequestHeader(r,A.opts.filesManagerUploadToAzure.params[r])}d.onload=function(){ze(Le(e))?function u(e,t,i,n,a){var r=this.status,o=this.response,s=this.responseXML,l=this.responseText;try{if(A.opts.filesManagerUploadToS3||A.opts.filesManagerUploadToAzure)if(201===r){var d;if(A.opts.filesManagerUploadToAzure){if(!1===A.events.trigger("filesManager.uploadedToAzure",[this.responseURL,a,o],!0))return A.edit.on(),!1;d=n}else d=Se(s,t);if(d){var f={link:d,text:e,response:o,type:i};_.set(t,f)}}else X(U,o||s,null,t);else if(200<=r&&r<300){var c=De(l,t);if(c){var p={link:c.link,text:e,response:o,type:i};_.set(t,p)}}else X(R,o||l,null,t)}catch(g){X(U,o||l,null,t)}}.call(d,e.name,n,Le(e),o,s):function m(e,t,i,n,a){var r=this.status,o=this.response,s=this.responseXML,l=this.responseText;try{if(A.opts.filesManagerUploadToS3||A.opts.filesManagerUploadToAzure)if(201==r){var d;if(A.opts.filesManagerUploadToAzure){if(!1===A.events.trigger("filesManager.uploadedToAzure",[this.responseURL,a,o],!0))return A.edit.on(),!1;d=n}else d=Se(s,t);if(d){var f={link:d,sanitize:!1,data:[],$existing_img:e,response:o||s,type:i};_.set(t,f)}}else X(U,o||s,e,t);else if(200<=r&&r<300){var c=De(l,t);if(c){var p={link:c.link,sanitize:!1,data:c,$existing_img:e,response:o||s,type:i};_.set(t,p)}}else X(R,o||l,e,t)}catch(g){X(U,o||l,e,t)}}.call(d,x,n,Le(e),o,s),S.has(n)||we(100,n,!0)},d.onerror=function(){X(U,this.response||this.responseText||this.responseXML,null,n)},d.upload.onprogress=function(e){!function i(e,t){e.lengthComputable&&we(e.loaded/e.total*100|0,t,!1)}(e,n)},d.onabort=function(e){!function i(e,t){X($,t,x,e)}(n,e)},d.send(A.opts.filesManagerUploadToAzure?e:a),E.set(n,d)}}function Be(l){A.events.$on(l,"click",".fr-upload-progress-layer",function(e){if(A.helpers.isMobile())return e.stopPropagation(),!1},!0),A.events.$on(l,"dragover dragenter",".fr-upload-progress-layer",function(e){e.preventDefault();for(var t=0;t<e.originalEvent.dataTransfer.types.length;t++)"Files"==e.originalEvent.dataTransfer.types[t]&&(e.originalEvent.dataTransfer.dropEffect="none");return!1},!0),A.events.$on(l,"dragleave dragend",".fr-upload-progress-layer",function(e){return e.preventDefault(),!1},!0),A.events.$on(l,"dragover dragenter",".fr-files-upload-layer",function(e){return I(this).addClass("fr-drop"),(A.browser.msie||A.browser.edge)&&e.preventDefault(),!1},!0),A.events.$on(l,"dragleave dragend",".fr-files-upload-layer",function(e){return I(this).removeClass("fr-drop"),(A.browser.msie||A.browser.edge)&&e.preventDefault(),!1},!0),A.events.$on(l,"click",".fr-insert-checkbox",function(e){if(this.classList.contains("fr-checkbox-disabled"))return this.children.target.disabled=!0,void(this.children.target.checked=!1);var t=parseInt(this.id.split("-").pop());D.set(t,this.children.target.checked);for(var i=l.find('.fr-command[data-cmd="insertAll"]'),n=l.find('.fr-command[data-cmd="deleteAll"]'),a=l.find('input.fr-file-insert-check[type="checkbox"]'),r=a.length,o=!0,s=0;s<r;s++)1==a[s].checked&&(o=!1);if(o?i.addClass("fr-disabled"):i.removeClass("fr-disabled"),o?n.addClass("fr-disabled"):n.removeClass("fr-disabled"),this.children.target.checked)l.find(".fr-file-"+this.id.split("-").pop()).get(0).setAttribute("draggable","true"),l.find(".fr-file-"+this.id.split("-").pop()).get(0).classList.remove("fr-unchecked");else{this.id.split("-").pop();l.find(".fr-file-"+this.id.split("-").pop()).get(0).setAttribute("draggable","false"),l.find(".fr-file-"+this.id.split("-").pop()).get(0).classList.add("fr-unchecked")}}),A.events.$on(l,"click",".fr-file-insert-button",function(e){this.classList.contains("fr-disabled")||_e(parseInt(this.id.split("-").pop()))}),A.events.$on(l,"click",".fr-file-autoplay-button",function(e){if(this.parentNode.classList.contains("fr-checkbox-disabled"))return this.disabled=!0,void(this.checked=!1);Re(parseInt(this.id.split("-").pop()))}),A.events.$on(l,"click",".fr-file-edit-button",function(e){var t=parseInt(this.id.split("-").pop());l.find(".fr-file-edit-button-".concat(t)).hasClass("fr-disabled")||Ie(t)}),A.events.$on(l,"click",".fr-file-view-button",function(e){var t=parseInt(this.id.split("-").pop());l.find(".fr-file-view-button-".concat(t)).hasClass("fr-disabled")||function h(e){if(!Pe(Le(_.get(e)))){var t=_.get(e).link,i=_.get(e).link;if(L.get(e)&&L.get(e).name?i=L.get(e).name:_.get(e).text&&(i=_.get(e).text),0===t.indexOf("blob:")&&A.browser.msie&&window.navigator&&window.navigator.msSaveBlob)window.navigator.msSaveBlob(L.get(e),i);else{var n=document.createElement("a");n.href=t,n.download=i,n.click()}return!1}var a=A.popups.get("filesManager.insert");if(0<a.find(".fr-file-view-image-"+e).length)a.find(".fr-file-view-image-"+e)[0].remove();else{for(var r=a.find(".fr-file-view"),o=0;o<r.length;o++)r.get(o).remove();var s=a.find(".fr-file-view-"+e);if(Ue(Le(_.get(e)))){var l='<div class="fr-file-view-modal">\n              <div class="fr-file-view-modal-content">\n              <div class="fr-file-view-close">&times;</div> \n                <img src="'+_.get(e).link+"\" class ='fr-file-view-image'/>\n                </div>\n              </div>";s[0].innerHTML=l+s[0].innerHTML}else if(Oe(Le(_.get(e)))){var d;if(_.get(e).hasOwnProperty("video")){var f=_.get(e).video.substring(_.get(e).video.indexOf("src")+3),c=f.substring(f.indexOf('"')+1);c=c.substring(0,c.indexOf('"')),d='<div class="fr-file-view-modal">\n        <div class="fr-file-view-modal-content ">\n          <div class="fr-file-view-close">&times;</div> \n          <iframe width="640" height="360" src="'.concat(c+"&autoplay=1&mute=1",'" allow="autoplay" frameborder="0" class = "fr-file-view-image"></iframe>\n        </div>\n      </div>')}else d='<div class="fr-file-view-modal">\n          <div class="fr-file-view-modal-content ">\n            <div class="fr-file-view-close">&times;</div> \n            <video controls src="'+_.get(e).link+"\"  class ='fr-file-view-image' autoplay></video>\n          </div>\n        </div>";s[0].innerHTML=d+s[0].innerHTML}else if($e(Le(_.get(e)))){var p='<div class="fr-file-view-modal">\n        <div class="fr-file-view-modal-content ">\n          <div  class="fr-file-view-close">&times;</div> \n          <audio controls="controls"  class =\'fr-file-view-image\' autoplay>\n\n          <source src="'.concat(_.get(e).link,'" type="').concat(Le(_.get(e)),'" />\n\n            Your browser does not support the audio element.\n          </audio>\n        </div>\n      </div>');s[0].innerHTML=p+s[0].innerHTML}else if(ze(Le(_.get(e)))){var g=_.get(e).link,u=_.get(e).text;if(g.endsWith(".pdf")||g.endsWith(".txt")){var m='<div class="fr-file-view-modal">\t\n              <div class="fr-file-view-modal-content " >\t\n              <div class="fr-file-view-close">&times;</div> \t\n              <iframe src="'.concat(g,"\" style='background-color: white;' height='50%' width='50%' title=\"").concat(u,'" class="fr-file fr-file-view-image"></iframe>\t\n            </div>\t\n            </div>');s[0].innerHTML=m+s[0].innerHTML}else if(0===g.indexOf("blob:")&&A.browser.msie&&window.navigator&&window.navigator.msSaveBlob)window.navigator.msSaveBlob(L.get(e),u);else{var v=document.createElement("a");v.href=g,v.download=u,v.click()}}}}(t)}),A.events.$on(l,"click",".fr-file-delete-button",function(e){q(parseInt(this.id.split("-").pop()))}),A.events.$on(l,"click",".fr-file-cancel-upload-button",function(e){!function i(e){var t=A.popups.get("filesManager.insert");t.find(".fr-file-item-right-"+e).get(0).innerHTML=ye(e),E.get(e).abort(),xe(e,100,!0),t.find(".fr-checkbox-file-"+e).get(0).disabled=!0}(parseInt(this.id.split("-").pop()))}),A.events.$on(l,"click",".fr-upload-delete-button",function(e){!function t(e){0!=E.get(e).readyState&&(E.get(e).abort(),xe(e,100,!0),E["delete"](e)),q(e)}(parseInt(this.id.split("-").pop()))}),A.events.$on(l,"click",".fr-file-view-close",function(e){l.find(".fr-file-view-modal").get(0).outerHTML=""}),A.events.$on(l,"click",".fr-plugins-enable",function(e){!function t(){m.forEach(function(e){A.opts.pluginsEnabled.indexOf(e)<0&&A.opts.pluginsEnabled.push(e)})}(),function i(e){for(var t in e)if(!A[t]){if(It.PLUGINS[t]&&A.opts.pluginsEnabled.indexOf(t)<0)continue;A[t]=new e[t](A),A[t]._init&&A[t]._init()}}(It.PLUGINS),A.popups.get("filesManager.insert").get(0).outerHTML="",Fe(),y(!0)}),A.events.$on(l,"click",".fr-plugins-cancel",function(e){A.popups.hide("filesManager.insert")}),A.events.$on(l,"drop",".fr-upload-progress",function(e){e.preventDefault(),e.stopPropagation()}),A.events.$on(l,"drop",".fr-files-upload-layer",function(e){e.preventDefault(),e.stopPropagation(),I(this).removeClass("fr-drop");var t=e.originalEvent.dataTransfer;if(t&&t.files){var i=l.data("instance")||A;i.events.disableBlur();for(var n=[],a=0;a<t.files.length;a++){var r=u;L.set(r,t.files[a]),ve(r),D.set(r,!1),n.push(r),u++}for(var o=0;o<n.length;o++)i.filesManager.upload(L.get(n[o]),t.files,x,n[o]);i.events.enableBlur()}},!0),A.helpers.isIOS()&&A.events.$on(l,"touchstart",'.fr-files-upload-layer input[type="file"]',function(){I(this).trigger("click")},!0),A.events.$on(l,"change",'.fr-files-upload-layer input[type="file"]',function(){if(this.files){var e=l.data("instance")||A;e.events.disableBlur(),l.find("input:focus").blur(),e.events.enableBlur();var t=[];if("undefined"!=typeof this.files&&0<this.files.length){for(var i=0;i<this.files.length;i++){var n=u;L.set(n,this.files[i]),ve(n),D.set(n,!1),++u,t.push(n)}for(var a=0;a<t.length;a++)e.filesManager.upload(L.get(t[a]),this.files,x,t[a])}}I(this).val("")},!0)}function Re(t){document.getElementById("fr-file-autoplay-button-"+t).checked?T.push(t):T=T.filter(function(e){return e!=t})}function Ue(e){return e&&"image"===e.split("/")[0]}function ze(e){return e&&"image"!=e.split("/")[0]&&e&&"video"!=e.split("/")[0]&&e&&"audio"!=e.split("/")[0]}function Oe(e){return e&&"video"===e.split("/")[0]}function $e(e){return e&&"audio"===e.split("/")[0]}function Pe(e){var t="audio/ogg",i="video/ogg";if(e==t||e==i||"image/webp"==e||"video/webm"==e){if(A.browser.msie||A.browser.edge||A.browser.safari)return!1;if(A.helpers.isMobile()){if(e==t||e==i)return!1;if(!A.helpers.isAndroid()&&!A.browser.chrome)return!1}}return!0}function Fe(e){if(e)return A.popups.onRefresh("filesManager.insert",b),A.popups.onHide("filesManager.insert",F),!0;var t,i,n="";A.opts.imageUpload||-1===A.opts.filesInsertButtons.indexOf("filesUpload")||A.opts.imageInsertButtons.splice(A.opts.filesInsertButtons.indexOf("filesUpload"),1);var a=A.button.buildList(A.opts.filesInsertButtons),r=A.button.buildList(A.opts.filesInsertButtons2);""!==a&&(n='<div class="fr-buttons fr-tabs">'.concat(a,'<span class="fr-align-right">').concat(r,"</span></div>"));var o=A.opts.filesInsertButtons.indexOf("filesUpload"),s=A.opts.filesInsertButtons.indexOf("filesByURL"),l=A.opts.filesInsertButtons.indexOf("filesEmbed"),d="";0<=o&&(t=" fr-active",0<=s&&s<o&&(t=""),d='<div class="fr-files-upload-layer'.concat(t,' fr-layer " id="fr-files-upload-layer-').concat(A.id,'"><div style="display:flex"><div class="fr-upload-section"><div class = \'fr-blue-decorator\'><div class = \'fr-cloud-icon\'><svg class = "fr-svg" focusable="false" width="26px" height="26px" viewBox = "0 0 24 24" xlmns = "http://w3.org/200/svg"><path d = \'M12 6.66667a4.87654 4.87654 0 0 1 4.77525 3.92342l0.29618 1.50268 1.52794 0.10578a2.57021 2.57021 0 0 1 -0.1827 5.13478H6.5a3.49774 3.49774 0 0 1 -0.3844 -6.97454l1.06682 -0.11341L7.678 9.29387A4.86024 4.86024 0 0 1 12 6.66667m0 -2A6.871 6.871 0 0 0 5.90417 8.37 5.49773 5.49773 0 0 0 6.5 19.33333H18.41667a4.57019 4.57019 0 0 0 0.32083 -9.13A6.86567 6.86567 0 0 0 12 4.66667Zm0.99976 7.2469h1.91406L11.99976 9 9.08618 11.91357h1.91358v3H11V16h2V14h-0.00024Z\'></path></svg></div>Drag & Drop One or More Files<br><div class="decorated"><span> OR </span></div> Click Browse Files </div> </div><div class="fr-form"><input type="file" accept="').concat(A.opts.filesManagerAllowedTypes.join(",").toLowerCase(),'" tabIndex="-1" aria - labelledby="fr-files-upload-layer-').concat(A.id,'"role="button" multiple></div> </div></div>'));var f="";0<=l&&(t=" fr-active",(o<l&&0<=o||s<l&&0<=s)&&(t=""),f='<div class="fr-files-embed-layer fr-layer'.concat(t,'" id="fr-files-embed-layer-').concat(A.id,'"><div class="fr-input-line padding-top-15"><textarea data-gramm_editor="false" style=\'height:60px\' id="fr-files-embed-layer-text').concat(A.id,'" type="text" placeholder="').concat(A.language.translate("Embedded Code"),'" tabIndex="1" aria-required="true" rows="5"></textarea></div><div class="fr-action-buttons"><button type="button" class="fr-command fr-submit" data-cmd="insertEmbed" tabIndex="2" role="button">').concat(A.language.translate("Insert"),"</button></div></div>"));var c="";0<=s&&(t=" fr-active",0<=o&&o<s&&(t=""),c='<div class="fr-files-by-url-layer'.concat(t,' fr-layer" id="fr-files-by-url-layer-').concat(A.id,'"><div class="fr-input-line fr-by-url-padding"><input id="fr-files-by-url-layer-text-').concat(A.id,'" type="text" placeholder="http://" tabIndex="1" aria-required="true"></div><div class="fr-action-buttons"><button type="button" class="fr-command fr-submit" data-cmd="filesInsertByURL" tabIndex="2" role="button">').concat(A.language.translate("Add"),"</button></div></div>"));var p={buttons:n,upload_layer:d,by_url_layer:c,embed_layer:f,upload_progress_layer:"<div class = ' fr-margin-16 fr-upload-progress' id=\"fr-upload-progress-layer-".concat(A.id,"\" ><div  class='fr-progress-bar-style'><div class='fr-progress-bar fr-none'></div></div><div id='filesList' class = 'fr-upload-progress-layer fr-layer'></div></div>"),progress_bar:'<div class="fr-files-progress-bar-layer fr-layer"><h3 tabIndex="-1" class="fr-message">Uploading</h3><div class="fr-loader"><span class="fr-progress"></span></div><div class="fr-action-buttons"><button type="button" class="fr-command fr-dismiss" data-cmd="filesDismissError" tabIndex="2" role="button">OK</button></div></div>'};return 1<=A.opts.imageInsertButtons.length&&(i=A.popups.create("filesManager.insert",p)),A.$wp&&A.events.$on(A.$wp,"scroll",function(){x&&A.popups.isVisible("filesManager.insert")&&replace()}),Be(i),A.popups.setPopupDimensions(i),i}function Ne(e){var t=e.split("/").pop();if(t.split(".").length<2){var i=new Date;return t+"-"+i.getDate()+"/"+(i.getMonth()+1)+"/"+i.getFullYear()}return t}function He(){x&&A.popups.get("image.alt").find("input").val(x.attr("alt")||"").trigger("change")}function Ke(){var e=A.popups.get("image.alt");e||(e=Ye()),Q(),A.popups.refresh("image.alt"),A.popups.setContainer("image.alt",A.$sc);var t=lt();dt()&&(t=t.find(".fr-img-wrap"));var i=t.offset().left+t.outerWidth()/2,n=t.offset().top+t.outerHeight();A.popups.show("image.alt",i,n,t.outerHeight(),!0)}function Ye(e){if(e)return A.popups.onRefresh("image.alt",He),!0;var t={buttons:'<div class="fr-buttons fr-tabs">'.concat(A.button.buildList(A.opts.imageAltButtons),"</div>"),alt_layer:'<div class="fr-image-alt-layer fr-layer fr-active" id="fr-image-alt-layer-'.concat(A.id,'"><div class="fr-input-line"><input id="fr-image-alt-layer-text-').concat(A.id,'" type="text" placeholder="').concat(A.language.translate("Alternative Text"),'" tabIndex="1"></div><div class="fr-action-buttons"><button type="button" class="fr-command fr-submit" data-cmd="imageSetAlt" tabIndex="2" role="button">').concat(A.language.translate("Update"),"</button></div></div>")},i=A.popups.create("image.alt",t);return A.$wp&&A.events.$on(A.$wp,"scroll.image-alt",function(){x&&A.popups.isVisible("image.alt")&&Ke()}),i}function Ve(){var e=A.popups.get("image.size");if(x)if(dt()){var t=x.parent();t.get(0).style.width||(t=x.parent().parent()),e.find('input[name="width"]').val(t.get(0).style.width).trigger("change"),e.find('input[name="height"]').val(t.get(0).style.height).trigger("change")}else e.find('input[name="width"]').val(x.get(0).style.width).trigger("change"),e.find('input[name="height"]').val(x.get(0).style.height).trigger("change")}function We(){var e=A.popups.get("image.size");e||(e=Ge()),Q(),A.popups.refresh("image.size"),A.popups.setContainer("image.size",A.$sc);var t=lt();dt()&&(t=t.find(".fr-img-wrap"));var i=t.offset().left+t.outerWidth()/2,n=t.offset().top+t.outerHeight();A.popups.show("image.size",i,n,t.outerHeight(),!0)}function Ge(e){if(e)return A.popups.onRefresh("image.size",Ve),!0;var t={buttons:'<div class="fr-buttons fr-tabs">'.concat(A.button.buildList(A.opts.imageSizeButtons),"</div>"),size_layer:'<div class="fr-image-size-layer fr-layer fr-active" id="fr-image-size-layer-'.concat(A.id,'"><div class="fr-image-group"><div class="fr-input-line"><input id="fr-image-size-layer-width-\'').concat(A.id,'" type="text" name="width" placeholder="').concat(A.language.translate("Width"),'" tabIndex="1"></div><div class="fr-input-line"><input id="fr-image-size-layer-height').concat(A.id,'" type="text" name="height" placeholder="').concat(A.language.translate("Height"),'" tabIndex="1"></div></div><div class="fr-action-buttons"><button type="button" class="fr-command fr-submit" data-cmd="imageSetSize" tabIndex="2" role="button">').concat(A.language.translate("Update"),"</button></div></div>")},i=A.popups.create("image.size",t);return A.$wp&&A.events.$on(A.$wp,"scroll.image-size",function(){x&&A.popups.isVisible("image.size")&&We()}),i}function Xe(e,t,i,n){return e.pageX=t,Y.call(this,e),e.pageX=e.pageX+i*Math.floor(Math.pow(1.1,n)),V.call(this,e),W.call(this,e),++n}function je(e){(e=e||lt())&&!1!==A.events.trigger("image.beforeRemove",[e])&&(A.popups.hideAll(),it(!0),A.undo.canDo()||A.undo.saveStep(),e.get(0)==A.el?e.removeAttr("src"):(e.get(0).parentNode&&"A"==e.get(0).parentNode.tagName?(A.selection.setBefore(e.get(0).parentNode)||A.selection.setAfter(e.get(0).parentNode)||e.parent().after(It.MARKERS),I(e.get(0).parentNode).remove()):(A.selection.setBefore(e.get(0))||A.selection.setAfter(e.get(0))||e.after(It.MARKERS),e.remove()),A.html.fillEmptyBlocks(),A.selection.restore()),A.undo.saveStep())}function qe(e){var t=e.which;if(x&&(t==It.KEYCODE.BACKSPACE||t==It.KEYCODE.DELETE))return e.preventDefault(),e.stopPropagation(),je(),!1;if(x&&t==It.KEYCODE.ESC){var i=x;return it(!0),A.selection.setAfter(i.get(0)),A.selection.restore(),e.preventDefault(),!1}if(!x||t!=It.KEYCODE.ARROW_LEFT&&t!=It.KEYCODE.ARROW_RIGHT)return x&&t===It.KEYCODE.TAB?(e.preventDefault(),e.stopPropagation(),it(!0),!1):x&&t!=It.KEYCODE.F10&&!A.keys.isBrowserAction(e)?(e.preventDefault(),e.stopPropagation(),!1):void 0;var n=x.get(0);return it(!0),t==It.KEYCODE.ARROW_LEFT?A.selection.setBefore(n):A.selection.setAfter(n),A.selection.restore(),e.preventDefault(),!1}function Ze(e){if(e&&"IMG"==e.tagName){if(A.node.hasClass(e,"fr-uploading")||A.node.hasClass(e,"fr-error")?e.parentNode.removeChild(e):A.node.hasClass(e,"fr-draggable")&&e.classList.remove("fr-draggable"),e.parentNode&&e.parentNode.parentNode&&A.node.hasClass(e.parentNode.parentNode,"fr-img-caption")){var t=e.parentNode.parentNode;t.removeAttribute("contenteditable"),t.removeAttribute("draggable"),t.classList.remove("fr-draggable");var i=e.nextSibling;i&&i.removeAttribute("contenteditable")}}else if(e&&e.nodeType==Node.ELEMENT_NODE)for(var n=e.querySelectorAll("img.fr-uploading, img.fr-error, img.fr-draggable"),a=0;a<n.length;a++)Ze(n[a])}function Je(e){var t=e.target.result,i=A.opts.imageDefaultWidth;i&&"auto"!=i&&(i+=A.opts.imageResizeWithPercent?"%":"px"),A.undo.saveStep(),A.html.insert('<img data-fr-image-pasted="true" src="'.concat(t,'"').concat(i?' style="width: '.concat(i,';"'):"",">"));var n=A.$el.find('img[data-fr-image-pasted="true"]');n&&ot(n,A.opts.imageDefaultDisplay,A.opts.imageDefaultAlign),A.events.trigger("paste.after")}function Qe(e,t){var i=new FileReader;i.onload=function n(e){var t=A.opts.imageDefaultWidth;t&&"auto"!=t&&(t+=A.opts.imageResizeWithPercent?"%":"px"),A.html.insert('<img data-fr-image-pasted="true" src="'.concat(e,'"').concat(t?' style="width: '.concat(t,';"'):"",">"));var i=A.$el.find('img[data-fr-image-pasted="true"]');i&&ot(i,A.opts.imageDefaultDisplay,A.opts.imageDefaultAlign),A.events.trigger("paste.after")}(t),i.readAsDataURL(e,t)}function et(e){if(e&&e.clipboardData&&e.clipboardData.items){var t=(e.clipboardData||window.clipboardData).getData("text/html")||"",i=(new DOMParser).parseFromString(t,"text/html").querySelector("img");if(i){if(!i)return!1;var n=i.src,a=null;if(e.clipboardData.types&&-1!=[].indexOf.call(e.clipboardData.types,"text/rtf")||e.clipboardData.getData("text/rtf"))a=e.clipboardData.items[0].getAsFile();else for(var r=0;r<e.clipboardData.items.length;r++)if(a=e.clipboardData.items[r].getAsFile()){It.IMAGE_TYPE=a.type;break}if(a)return Qe(a,n),!1}else{var o=null;if(e.clipboardData.types&&-1!=[].indexOf.call(e.clipboardData.types,"text/rtf")||e.clipboardData.getData("text/rtf"))o=e.clipboardData.items[0].getAsFile();else for(var s=0;s<e.clipboardData.items.length&&!(o=e.clipboardData.items[s].getAsFile());s++);if(o)return function l(e){var t=new FileReader;t.onload=Je,t.readAsDataURL(e)}(o),!1}}}function tt(e){return e=e.replace(/<img /gi,'<img data-fr-image-pasted="true" ')}function it(e){x&&(function t(){return nt}()||!0===e)&&(A.toolbar.enable(),l&&l.removeClass("fr-active"),A.popups.hide("image.edit"),x=null,rt(),d=null,f&&f.hide())}var nt=!1;function at(){nt=!0}function rt(){nt=!1}function ot(e,t,i){!A.opts.htmlUntouched&&A.opts.useClasses?(I(e).removeClass("fr-fil fr-fir fr-dib fr-dii"),i&&I(e).addClass("fr-fi".concat(i[0])),t&&I(e).addClass("fr-di".concat(t[0]))):"inline"==t?(e.css({display:"inline-block",verticalAlign:"bottom",margin:A.opts.imageDefaultMargin}),"center"==i?e.css({"float":"none",marginBottom:"",marginTop:"",maxWidth:"calc(100% - ".concat(2*A.opts.imageDefaultMargin,"px)"),textAlign:"center"}):"left"==i?e.css({"float":"left",marginLeft:0,maxWidth:"calc(100% - ".concat(A.opts.imageDefaultMargin,"px)"),textAlign:"left"}):e.css({"float":"right",marginRight:0,maxWidth:"calc(100% - ".concat(A.opts.imageDefaultMargin,"px)"),textAlign:"right"})):"block"==t&&(e.css({display:"block","float":"none",verticalAlign:"top",margin:"".concat(A.opts.imageDefaultMargin,"px auto"),textAlign:"center"}),"left"==i?e.css({marginLeft:0,textAlign:"left"}):"right"==i&&e.css({marginRight:0,textAlign:"right"}))}function st(){return x}function lt(){return dt()?x.parents(".fr-img-caption").first():x}function dt(){return!!x&&0<x.parents(".fr-img-caption").length}return{_init:function ft(){var n;(function e(){A.events.$on(A.$el,A._mousedown,"IMG"==A.el.tagName?null:'img:not([contenteditable="false"])',function(e){if("false"==I(this).parents("contenteditable").not(".fr-element").not(".fr-img-caption").not("body").first().attr("contenteditable"))return!0;A.helpers.isMobile()||A.selection.clear(),t=!0,A.popups.areVisible()&&A.events.disableBlur(),A.browser.msie&&(A.events.disableBlur(),A.$el.attr("contenteditable",!1)),A.draggable||"touchstart"==e.type||e.preventDefault(),e.stopPropagation()}),A.events.$on(A.$el,A._mousedown,".fr-img-caption .fr-inner",function(e){A.core.hasFocus()||A.events.focus(),e.stopPropagation()}),A.events.$on(A.$el,"paste",".fr-img-caption .fr-inner",function(e){!0===A.opts.toolbarInline&&(A.toolbar.hide(),e.stopPropagation())}),A.events.$on(A.$el,A._mouseup,"IMG"==A.el.tagName?null:'img:not([contenteditable="false"])',function(e){if("false"==I(this).parents("contenteditable").not(".fr-element").not(".fr-img-caption").not("body").first().attr("contenteditable"))return!0;t&&(t=!1,e.stopPropagation(),A.browser.msie&&(A.$el.attr("contenteditable",!0),A.events.enableBlur()))}),A.events.on("keyup",function(e){if(e.shiftKey&&""===A.selection.text().replace(/\n/g,"")&&A.keys.isArrow(e.which)){var t=A.selection.element(),i=A.selection.endElement();t&&"IMG"==t.tagName?Ee(I(t)):i&&"IMG"==i.tagName&&Ee(I(i))}},!0),A.events.on("window.mousedown",at),A.events.on("window.touchmove",rt),A.events.on("mouseup window.mouseup",function(){if(x)return it(),!1;rt()}),A.events.on("commands.mousedown",function(e){0<e.parents(".fr-toolbar").length&&it()}),A.events.on("image.resizeEnd",function(){A.opts.iframe&&A.size.syncIframe()}),A.events.on("blur image.hideResizer commands.undo commands.redo element.dropped",function(){it(!(t=!1))}),A.events.on("modals.hide",function(){x&&A.selection.clear()}),A.events.on("image.resizeEnd",function(){A.win.getSelection&&Ee(x)}),A.opts.imageAddNewLine&&A.events.on("image.inserted",function(e){var t=e.get(0);for(t.nextSibling&&"BR"===t.nextSibling.tagName&&(t=t.nextSibling);t&&!A.node.isElement(t);)t=A.node.isLastSibling(t)?t.parentNode:null;A.node.isElement(t)&&(A.opts.enter===It.ENTER_BR?e.after("<br>"):I(A.node.blockParent(e.get(0))).after("<".concat(A.html.defaultTag(),"><br></").concat(A.html.defaultTag(),">")))})})(),"IMG"==A.el.tagName&&A.$el.addClass("fr-view"),A.helpers.isMobile()&&(A.events.$on(A.$el,"touchstart","IMG"==A.el.tagName?null:'img:not([contenteditable="false"])',function(){fe=!1}),A.events.$on(A.$el,"touchmove",function(){fe=!0})),A.$wp?(A.events.on("window.keydown keydown",qe,!0),A.events.on("keyup",function(e){if(x&&e.which==It.KEYCODE.ENTER)return!1},!0),A.events.$on(A.$el,"keydown",function(){var e=A.selection.element();(e.nodeType===Node.TEXT_NODE||"BR"==e.tagName&&A.node.isLastSibling(e))&&(e=e.parentNode),A.node.hasClass(e,"fr-inner")||(A.node.hasClass(e,"fr-img-caption")||(e=I(e).parents(".fr-img-caption").get(0)),A.node.hasClass(e,"fr-img-caption")&&(A.opts.trackChangesEnabled||I(e).after(It.INVISIBLE_SPACE+It.MARKERS),A.selection.restore()))})):A.events.$on(A.$win,"keydown",qe),A.events.on("toolbar.esc",function(){if(x){if(A.$wp)A.events.disableBlur(),A.events.focus();else{var e=x;it(!0),A.selection.setAfter(e.get(0)),A.selection.restore()}return!1}},!0),A.events.on("toolbar.focusEditor",function(){if(x)return!1},!0),A.events.on("window.cut window.copy",function(e){if(x&&A.popups.isVisible("image.edit")&&!A.popups.get("image.edit").find(":focus").length){var t=lt();dt()?(t.before(It.START_MARKER),t.after(It.END_MARKER),A.selection.restore(),A.paste.saveCopiedText(t.get(0).outerHTML,t.text())):A.paste.saveCopiedText(x.get(0).outerHTML,x.attr("alt")),"copy"==e.type?setTimeout(function(){Ee(x)}):(it(!0),A.undo.saveStep(),setTimeout(function(){A.undo.saveStep()},0))}},!0),A.browser.msie&&A.events.on("keydown",function(e){if(!A.selection.isCollapsed()||!x)return!0;var t=e.which;t==It.KEYCODE.C&&A.keys.ctrlKey(e)?A.events.trigger("window.copy"):t==It.KEYCODE.X&&A.keys.ctrlKey(e)&&A.events.trigger("window.cut")}),A.events.$on(I(A.o_win),"keydown",function(e){var t=e.which;if(x&&t==It.KEYCODE.BACKSPACE)return e.preventDefault(),!1}),A.events.$on(A.$win,"keydown",function(e){var t=e.which;x&&x.hasClass("fr-uploading")&&t==It.KEYCODE.ESC&&x.trigger("abortUpload")}),A.events.on("destroy",function(){x&&x.hasClass("fr-uploading")&&x.trigger("abortUpload")}),A.events.on("paste.before",et),A.events.on("paste.beforeCleanup",tt),A.events.on("html.processGet",Ze),A.opts.imageOutputSize&&A.events.on("html.beforeGet",function(){n=A.el.querySelectorAll("img");for(var e=0;e<n.length;e++){var t=n[e].style.width||I(n[e]).width(),i=n[e].style.height||I(n[e]).height();t&&n[e].setAttribute("width","".concat(t).replace(/px/,"")),i&&n[e].setAttribute("height","".concat(i).replace(/px/,""))}}),A.opts.iframe&&A.events.on("image.loaded",A.size.syncIframe),A.events.$on(I(A.o_win),"orientationchange.image",function(){setTimeout(function(){x&&Ee(x)},100)}),function a(e){if(e)return A.$wp&&A.events.$on(A.$wp,"scroll.image-edit",function(){x&&A.popups.isVisible("image.edit")&&A.events.disableBlur()}),!0;var t="";if(0<A.opts.imageEditButtons.length){var i={buttons:t+='<div class="fr-buttons"> \n        '.concat(A.button.buildList(A.opts.imageEditButtons),"\n        </div>")};return A.popups.create("image.edit",i)}return!1}(!0),Fe(!0),Ge(!0),Ye(!0),A.events.on("node.remove",function(e){if("IMG"==e.get(0).tagName)return je(e),!1}),A.events.on("popups.hide.filesManager.insert",function(e){A.filesManager.minimizePopup(C)})},showInsertPopup:y,showLayer:function ct(e){var t,i,n=A.popups.get("filesManager.insert");if(x||A.opts.toolbarInline){if(x){var a=lt();dt()&&(a=a.find(".fr-img-wrap")),i=a.offset().top+a.outerHeight(),t=a.offset().left}}else{var r=A.$tb.find('.fr-command[data-cmd="insertFiles"]');t=r.offset().left,i=r.offset().top+(A.opts.toolbarBottom?10:r.outerHeight()-10)}!x&&A.opts.toolbarInline&&(i=n.offset().top-A.helpers.getPX(n.css("margin-top")),n.hasClass("fr-above")&&(i+=n.outerHeight())),n.find(".fr-layer").removeClass("fr-active"),n.find(".fr-".concat(e,"-layer")).addClass("fr-active"),n.find(".fr-upload-progress-layer").addClass("fr-active"),A.popups.show("filesManager.insert",t,i,x?x.outerHeight():0),A.accessibility.focusPopup(n)},refreshUploadButton:function pt(e){var t=A.popups.get("filesManager.insert");t&&t.find(".fr-files-upload-layer").hasClass("fr-active")&&e.addClass("fr-active").attr("aria-pressed",!0)},refreshByURLButton:function gt(e){var t=A.popups.get("filesManager.insert");t&&t.find(".fr-files-by-url-layer").hasClass("fr-active")&&e.addClass("fr-active").attr("aria-pressed",!0)},upload:Te,insertByURL:function ut(){for(var e,t=A.popups.get("filesManager.insert").find(".fr-files-by-url-layer input"),i=t.val().trim().split(/[ ,]+/),n=[],a=0,r=0;r<i.length;r++)e=i[r],new RegExp("^(https?:\\/\\/)?((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|((\\d{1,3}\\.){3}\\d{1,3}))(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*(\\?[;&a-z\\d%_.~+=-]*)?(\\#[-a-z\\d_]*)?$","i").test(e)&&(n[a]=i[r],a++);if(0!=n.length){if(0<t.val().trim().length&&0<n.length){var s=[],l=[],d=u,f=n.length;n.forEach(function(e,i){if(0==e.trim().length)u==d+--f&&Me(s,l);else{Q(),J(),ee(A.language.translate("Loading file(s)"));var n=e.trim(),t=function o(e){if(void 0===e)return e;var t=null;if(/^http/.test(e)||(e="https://".concat(e)),A.helpers.isURL(e))for(var i=0;i<It.VIDEO_PROVIDERS.length;i++){var n=It.VIDEO_PROVIDERS[i];if(n.test_regex.test(e)&&new RegExp(A.opts.videoAllowedProviders.join("|")).test(n.provider)){t=e.replace(n.url_regex,n.url_text),t=n.html.replace(/\{url\}/,t);break}}return t}(n);if(t){var a={link:n,name:n,type:"video/url",size:2,video:t};L.set(d+i,a),ve(d+i),Q(),J(),ee(A.language.translate("Loading file(s)")),_.set(d+i,a),++u==d+f&&Me(s,l)}else{var r=new XMLHttpRequest;r.onload=function(){if(200==this.status){var e=new Blob([this.response],{type:this.response.type||""});e.name=Ne(n),e.link=n,Ue(this.response.type)?(e.sanitize=!0,e.existing_image=x):ze(this.response.type)&&(e.text=Ne(n)),s.push(e),l.push(d+i),L.set(d+i,e),ve(d+i),(-1<P.indexOf(Le(e))||!Le(e))&&X(O,null,null,d+i)}else{var t=new Blob([this.response],{type:this.response.type||" "});t.name=Ne(n),t.link=n,L.set(d+i,t),ve(d+i),X(B,this.response,x,d+i)}Q(),J(),ee(A.language.translate("Loading file(s)")),++u==d+f&&Me(s,l)},r.onerror=function(){var e={link:n,name:Ne(n),size:0,type:""};X(9,this.response,x,d+i);var t=u;L.set(t,e),ve(t),Q(),J(),ee(A.language.translate("Loading file(s)")),++u==d+f&&Me(s,l)},r.open("GET","".concat(A.opts.imageCORSProxy,"/").concat(n),!0),r.responseType="blob",r.send()}}}),t.val(""),t.blur()}}else te(A.language.translate("Url entered is invalid. Please try again."))},insertAllFiles:function mt(){a=[];var e=A.popups.get("filesManager.insert");g=-1,c=null,e.find(".fr-insert-checkbox").toArray().forEach(function n(e,t,i){e.children.target.checked&&(a.push(parseInt(e.id.split("-").pop())),Ue(_.get(parseInt(e.id.split("-").pop())).type)&&-1==g&&(g=t))}),Ce(a),j()},deleteAllFiles:function e(){A.popups.get("filesManager.insert").find(".fr-insert-checkbox").toArray().forEach(function a(e,t,i){if(e.children.target.checked){var n=parseInt(e.id.split("-").pop());E.has(n)&&E["delete"](n),q(n)}}),j()},get:st,getEl:lt,insert:_e,showProgressBar:J,remove:je,hideProgressBar:Q,applyStyle:function vt(e,t,i){if(void 0===t&&(t=A.opts.imageStyles),void 0===i&&(i=A.opts.imageMultipleStyles),!x)return!1;var n=lt();if(!i){var a=Object.keys(t);a.splice(a.indexOf(e),1),n.removeClass(a.join(" "))}"object"==Lt(t[e])?(n.removeAttr("style"),n.css(t[e].style)):n.toggleClass(e),Ee(x)},showAltPopup:Ke,showSizePopup:We,setAlt:function ht(e){if(x){var t=A.popups.get("image.alt");x.attr("alt",e||t.find("input").val()||""),t.find("input:focus").blur(),Ee(x)}},setSize:function bt(e,t){if(x){var i=A.popups.get("image.size");e=e||i.find('input[name="width"]').val()||"",t=t||i.find('input[name="height"]').val()||"";var n=/^[\d]+((px)|%)*$/g;x.removeAttr("width").removeAttr("height"),e.match(n)?x.css("width",e):x.css("width",""),t.match(n)?x.css("height",t):x.css("height",""),dt()&&(x.parents(".fr-img-caption").removeAttr("width").removeAttr("height"),e.match(n)?x.parents(".fr-img-caption").css("width",e):x.parents(".fr-img-caption").css("width",""),t.match(n)?x.parents(".fr-img-caption").css("height",t):x.parents(".fr-img-caption").css("height","")),i&&i.find("input:focus").blur(),Ee(x)}},toggleCaption:function yt(){var e;if(x&&!dt()){(e=x).parent().is("a")&&(e=x.parent());var t,i,n=x.parents("ul")&&0<x.parents("ul").length?x.parents("ul"):x.parents("ol")&&0<x.parents("ol").length?x.parents("ol"):[];if(0<n.length){var a=n.find("li").length,r=x.parents("li"),o=document.createElement("li");a-1===r.index()&&(n.append(o),o.innerHTML="&nbsp;")}e.attr("style")&&(i=-1<(t=e.attr("style").split(":")).indexOf("width")?t[t.indexOf("width")+1].replace(";",""):"");var s=A.opts.imageResizeWithPercent?(-1<i.indexOf("px")?null:i)||"100%":x.width()+"px";e.wrap('<div class="fr-img-space-wrap"><span '+(A.browser.mozilla?"":'contenteditable="false"')+'class="fr-img-caption '+x.attr("class")+'" style="'+(A.opts.useClasses?"":e.attr("style"))+'" draggable="false"></span><p class="fr-img-space-wrap2">&nbsp;</p></div>'),e.wrap('<span class="fr-img-wrap"></span>'),x.after('<span class="fr-inner"'.concat(A.browser.mozilla?"":' contenteditable="true"',">").concat(It.START_MARKER).concat(A.language.translate("Image Caption")).concat(It.END_MARKER,"</span>")),x.removeAttr("class").removeAttr("style").removeAttr("width"),x.parents(".fr-img-caption").css("width",s),it(!0),A.selection.restore()}else e=lt(),x.insertAfter(e),x.attr("class",e.attr("class").replace("fr-img-caption","")).attr("style",e.attr("style")),e.remove(),Ee(x)},refreshEmbedButton:function wt(e){var t=A.popups.get("filesManager.insert");t&&t.find(".fr-files-embed-layer").hasClass("fr-active")&&e.addClass("fr-active").attr("aria-pressed",!0)},insertEmbed:function xt(e){void 0===e&&(e=A.popups.get("filesManager.insert").find(".fr-files-embed-layer textarea").val()||""),0===e.length||!It.VIDEO_EMBED_REGEX.test(e)&&!It.IMAGE_EMBED_REGEX.test(e)?(te(A.language.translate("Something went wrong. Please try again.")),It.VIDEO_EMBED_REGEX.test(e)&&A.events.trigger("video.codeError",[e])):function s(e,t){var i,n;It.VIDEO_EMBED_REGEX.test(e)?(i="video",n=A.opts.videoSplitHTML):It.IMAGE_EMBED_REGEX.test(e)&&(i="image",n=A.opts.imageSplitHTML),A.events.focus(!0),A.selection.restore();var a=!1;x&&(je(),a=!0),A.html.insert('<span id="fr-inserted-file" contenteditable="true" draggable="true" class="fr-'.concat(i,' fr-jiv fr-deletable">').concat(e,"</span>"),!1,n),A.popups.hide("filesManager.insert");var r=A.$el.find(".fr-jiv");r.removeClass("fr-jiv"),"video"==i&&(r.toggleClass("fr-rv",A.opts.videoResponsive),function o(e,t,i){!A.opts.htmlUntouched&&A.opts.useClasses?(e.removeClass("fr-fvl fr-fvr fr-dvb fr-dvi"),e.addClass("fr-fv".concat(i[0]," fr-dv").concat(t[0]))):"inline"==t?(e.css({display:"inline-block"}),"center"==i?e.css({"float":"none"}):"left"==i?e.css({"float":"left"}):e.css({"float":"right"})):(e.css({display:"block",clear:"both"}),"left"==i?e.css({textAlign:"left"}):"right"==i?e.css({textAlign:"right"}):e.css({textAlign:"center"}))}(r,A.opts.videoDefaultDisplay,A.opts.videoDefaultAlign),r.toggleClass("fr-draggable",A.opts.videoMove),A.events.trigger(a?"video.replaced":"video.inserted",[r])),"image"==i&&(ot(r,A.opts.imageDefaultDisplay,A.opts.imageDefaultAlign),r.find("img").removeClass("fr-dii"),r.find("img").addClass("fr-dib"),r.toggleClass("fr-draggable",A.opts.imageMove),A.events.trigger(a?"image.replaced":"image.inserted",[r])),t&&(c=r,A.selection.clear(),A.toolbar.disable(),A.video._editVideo(c))}(e)},hasCaption:dt,exitEdit:it,edit:Ee,cancelFileInsert:function Mt(){this.file_manager_dialog_open=!1,E.forEach(function(e,t){4!=e.readyState&&(e.abort(),q(t))});var e=A.popups.get("filesManager.insert");e.find(".fr-progress-bar").removeClass("fr-display-block").addClass("fr-none"),e.find('.fr-command[data-cmd="filesUpload"]').removeClass("fr-disabled"),e.find('.fr-command[data-cmd="filesByURL"]').removeClass("fr-disabled"),e.find('.fr-command[data-cmd="filesEmbed"]').removeClass("fr-disabled"),r=0,E=new Map,k=new Map,Z(),A.popups.hide("filesManager.insert")},minimizePopup:function Et(e){this.file_manager_dialog_open=!1,A.popups.hide("filesManager.insert"),Z()},editImage:Ie,saveImage:function kt(e){var t=_.get(o);t.link=window.URL.createObjectURL(new Blob(e,{type:"image/png"})),_.set(o,t)},_showErrorMessage:te,_showFileErrorMessage:ie,getFileThumbnail:be,deleteFile:q,checkAutoplay:Re,checkInsertAllState:j,_disableInsertCheckbox:G,_getFileType:Le,isChildWindowOpen:function At(){return p},setChildWindowState:function Ct(e){e!==undefined&&(p=e)},resetAllFilesCheckbox:Z}},It.DefineIcon("insertFiles",{NAME:"image",SVG_KEY:"fileManager"}),It.RegisterShortcut(It.KEYCODE.P,"insertFiles",null,"P"),It.RegisterCommand("insertFiles",{title:"Insert Files",undo:!1,focus:!0,refreshAfterCallback:!1,popup:!0,callback:function(){this.popups.isVisible("filesManager.insert")?(this.$el.find(".fr-marker").length&&(this.events.disableBlur(),this.selection.restore()),this.popups.hide("filesManager.insert")):this.filesManager.showInsertPopup()},plugin:"filesManager"}),It.DefineIcon("cloudIcon",{NAME:"cloudIcon",SVG_KEY:"uploadFiles"}),It.DefineIcon("filesUpload",{NAME:"uploadFiles",SVG_KEY:"uploadFiles"}),It.RegisterCommand("filesUpload",{title:"Upload Files",undo:!1,focus:!1,toggle:!0,callback:function(){this.filesManager.showLayer("files-upload")},refresh:function(e){this.filesManager.refreshUploadButton(e)}}),It.DefineIcon("filesByURL",{NAME:"link",SVG_KEY:"insertLink"}),It.RegisterCommand("filesByURL",{title:"By URL",undo:!1,focus:!1,toggle:!0,callback:function(){this.filesManager.showLayer("files-by-url")},refresh:function(e){this.filesManager.refreshByURLButton(e)}}),It.DefineIcon("filesEmbed",{NAME:"code",SVG_KEY:"codeView"}),It.RegisterCommand("filesEmbed",{title:"Embedded Code",undo:!1,focus:!1,toggle:!0,callback:function(){this.filesManager.showLayer("files-embed")},refresh:function(e){this.filesManager.refreshEmbedButton(e)}}),It.DefineIcon("insertAll",{NAME:"insertAll",SVG_KEY:"fileInsert"}),It.RegisterCommand("insertAll",{title:"Insert",undo:!1,focus:!1,toggle:!0,disabled:!0,callback:function(){this.filesManager.insertAllFiles()}}),It.DefineIcon("deleteAll",{NAME:"remove",SVG_KEY:"remove"}),It.RegisterCommand("deleteAll",{title:"Delete",undo:!1,focus:!1,toggle:!0,disabled:!0,callback:function(){this.filesManager.deleteAllFiles()}}),It.DefineIcon("cancel",{NAME:"cancel",SVG_KEY:"cancel"}),It.RegisterCommand("cancel",{title:"Cancel",undo:!1,focus:!1,toggle:!0,callback:function(){this.filesManager.cancelFileInsert()},refresh:function(e){}}),It.DefineIcon("minimize",{NAME:"minimize",SVG_KEY:"minimize"}),It.RegisterCommand("minimize",{title:"Minimize",undo:!1,focus:!1,toggle:!0,callback:function(){this.filesManager.minimizePopup("image.insert",!0)},refresh:function(e){this.filesManager.refreshEmbedButton(e)}}),It.RegisterCommand("filesInsertByURL",{title:"Insert Image",undo:!0,refreshAfterCallback:!1,callback:function(){this.filesManager.insertByURL()},refresh:function(e){e.text(this.language.translate("Add"))}}),It.RegisterCommand("imageInsertByUpload",{title:"Insert",undo:!0,refreshAfterCallback:!1,callback:function(e,t){},refresh:function(e){}}),It.RegisterCommand("viewImage",{title:"View Image",undo:!0,refreshAfterCallback:!1,callback:function(e,t){},refresh:function(e){}}),It.RegisterCommand("insertEmbed",{undo:!0,focus:!0,callback:function(){this.filesManager.insertEmbed(),this.popups.get("filesManager.insert").find("textarea")[0].value="",this.popups.get("filesManager.insert").find("textarea").removeClass("fr-not-empty")}}),It.RegisterCommand("filesDismissError",{title:"OK",undo:!1,callback:function(){this.filesManager.hideProgressBar(!0)}})});
/*!
 * froala_editor v4.0.10 (https://www.froala.com/wysiwyg-editor)
 * License https://froala.com/wysiwyg-editor/terms/
 * Copyright 2014-2022 Froala Labs
 */

!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?module.exports=r():"function"==typeof define&&define.amd?define(r):e.Showdown=r()}(this,function(){"use strict";function p(e){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function t(e){var r={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:"Remove only spaces, ' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,description:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,description:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,description:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,description:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,description:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",description:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,description:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,description:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,description:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,description:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,description:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},completeHTMLDocument:{defaultValue:!1,description:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,description:"Enable support for document metadata (defined at the top of the document between `\xab\xab\xab` and `\xbb\xbb\xbb` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,description:"Split adjacent blockquote blocks",type:"boolean"}};if(!1===e)return JSON.parse(JSON.stringify(r));var a={};for(var t in r)r.hasOwnProperty(t)&&(a[t]=r[t].defaultValue);return a}var w={},a={},h={},_=t(!0),g="vanilla",m={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:t(!0),allOn:function k(){var e=t(!0),r={};for(var a in e)e.hasOwnProperty(a)&&(r[a]=!0);return r}()};function f(e,r){var a=r?"Error in "+r+" extension->":"Error in unnamed extension",t={valid:!0,error:""};w.helper.isArray(e)||(e=[e]);for(var n=0;n<e.length;++n){var o=a+" sub-extension "+n+": ",s=e[n];if("object"!==p(s))return t.valid=!1,t.error=o+"must be an object, but "+p(s)+" given",t;if(!w.helper.isString(s.type))return t.valid=!1,t.error=o+'property "type" must be a string, but '+p(s.type)+" given",t;var i=s.type=s.type.toLowerCase();if("language"===i&&(i=s.type="lang"),"html"===i&&(i=s.type="output"),"lang"!==i&&"output"!==i&&"listener"!==i)return t.valid=!1,t.error=o+"type "+i+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',t;if("listener"===i){if(w.helper.isUndefined(s.listeners))return t.valid=!1,t.error=o+'. Extensions of type "listener" must have a property called "listeners"',t}else if(w.helper.isUndefined(s.filter)&&w.helper.isUndefined(s.regex))return t.valid=!1,t.error=o+i+' extensions must define either a "regex" property or a "filter" method',t;if(s.listeners){if("object"!==p(s.listeners))return t.valid=!1,t.error=o+'"listeners" property must be an object but '+p(s.listeners)+" given",t;for(var l in s.listeners)if(s.listeners.hasOwnProperty(l)&&"function"!=typeof s.listeners[l])return t.valid=!1,t.error=o+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+l+" must be a function but "+p(s.listeners[l])+" given",t}if(s.filter){if("function"!=typeof s.filter)return t.valid=!1,t.error=o+'"filter" must be a function, but '+p(s.filter)+" given",t}else if(s.regex){if(w.helper.isString(s.regex)&&(s.regex=new RegExp(s.regex,"g")),!(s.regex instanceof RegExp))return t.valid=!1,t.error=o+'"regex" property must either be a string or a RegExp object, but '+p(s.regex)+" given",t;if(w.helper.isUndefined(s.replace))return t.valid=!1,t.error=o+'"regex" extensions must implement a replace string or function',t}}return t}function o(e,r){return"\xa8E"+r.charCodeAt(0)+"E"}w.helper={},w.extensions={},w.setOption=function(e,r){return _[e]=r,this},w.getOption=function(e){return _[e]},w.getOptions=function(){return _},w.resetOptions=function(){_=t(!0)},w.setFlavor=function(e){if(!m.hasOwnProperty(e))throw Error(e+" flavor was not found");w.resetOptions();var r=m[e];for(var a in g=e,r)r.hasOwnProperty(a)&&(_[a]=r[a])},w.getFlavor=function(){return g},w.getFlavorOptions=function(e){if(m.hasOwnProperty(e))return m[e]},w.getDefaultOptions=function(e){return t(e)},w.subParser=function(e,r){if(w.helper.isString(e)){if(void 0===r){if(a.hasOwnProperty(e))return a[e];throw Error("SubParser named "+e+" not registered!")}a[e]=r}},w.extension=function(e,r){if(!w.helper.isString(e))throw Error("Extension 'name' must be a string");if(e=w.helper.stdExtName(e),w.helper.isUndefined(r)){if(!h.hasOwnProperty(e))throw Error("Extension named "+e+" is not registered!");return h[e]}"function"==typeof r&&(r=r()),w.helper.isArray(r)||(r=[r]);var a=f(r,e);if(!a.valid)throw Error(a.error);h[e]=r},w.getAllExtensions=function(){return h},w.removeExtension=function(e){delete h[e]},w.resetExtensions=function(){h={}},w.validateExtension=function(e){var r=f(e,null);return!!r.valid},w.hasOwnProperty("helper")||(w.helper={}),w.helper.isString=function(e){return"string"==typeof e||e instanceof String},w.helper.isFunction=function(e){return e&&"[object Function]"==={}.toString.call(e)},w.helper.isArray=function(e){return Array.isArray(e)},w.helper.isUndefined=function(e){return void 0===e},w.helper.forEach=function(e,r){if(w.helper.isUndefined(e))throw new Error("obj param is required");if(w.helper.isUndefined(r))throw new Error("callback param is required");if(!w.helper.isFunction(r))throw new Error("callback param must be a function/closure");if("function"==typeof e.forEach)e.forEach(r);else if(w.helper.isArray(e))for(var a=0;a<e.length;a++)r(e[a],a,e);else{if("object"!==p(e))throw new Error("obj does not seem to be an array or an iterable object");for(var t in e)e.hasOwnProperty(t)&&r(e[t],t,e)}},w.helper.stdExtName=function(e){return e.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},w.helper.escapeCharactersCallback=o,w.helper.escapeCharacters=function(e,r,a){var t="(["+r.replace(/([\[\]\\])/g,"\\$1")+"])";a&&(t="\\\\"+t);var n=new RegExp(t,"g");return e=e.replace(n,o)},w.helper.unescapeHTMLEntities=function(e){return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")};var b=function b(e,r,a,t){var n,o,s,i,l,c=t||"",u=-1<c.indexOf("g"),d=new RegExp(r+"|"+a,"g"+c.replace(/g/g,"")),p=new RegExp(r,c.replace(/g/g,"")),h=[];do{for(n=0;s=d.exec(e);)if(p.test(s[0]))n++||(i=(o=d.lastIndex)-s[0].length);else if(n&&!--n){l=s.index+s[0].length;var _={left:{start:i,end:o},match:{start:o,end:s.index},right:{start:s.index,end:l},wholeMatch:{start:i,end:l}};if(h.push(_),!u)return h}}while(n&&(d.lastIndex=o));return h};w.helper.matchRecursiveRegExp=function(e,r,a,t){for(var n=b(e,r,a,t),o=[],s=0;s<n.length;++s)o.push([e.slice(n[s].wholeMatch.start,n[s].wholeMatch.end),e.slice(n[s].match.start,n[s].match.end),e.slice(n[s].left.start,n[s].left.end),e.slice(n[s].right.start,n[s].right.end)]);return o},w.helper.replaceRecursiveRegExp=function(e,r,a,t,n){if(!w.helper.isFunction(r)){var o=r;r=function r(){return o}}var s=b(e,a,t,n),i=e,l=s.length;if(0<l){var c=[];0!==s[0].wholeMatch.start&&c.push(e.slice(0,s[0].wholeMatch.start));for(var u=0;u<l;++u)c.push(r(e.slice(s[u].wholeMatch.start,s[u].wholeMatch.end),e.slice(s[u].match.start,s[u].match.end),e.slice(s[u].left.start,s[u].left.end),e.slice(s[u].right.start,s[u].right.end))),u<l-1&&c.push(e.slice(s[u].wholeMatch.end,s[u+1].wholeMatch.start));s[l-1].wholeMatch.end<e.length&&c.push(e.slice(s[l-1].wholeMatch.end)),i=c.join("")}return i},w.helper.regexIndexOf=function(e,r,a){if(!w.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(r instanceof RegExp==!1)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";var t=e.substring(a||0).search(r);return 0<=t?t+(a||0):t},w.helper.splitAtIndex=function(e,r){if(!w.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[e.substring(0,r),e.substring(r)]},w.helper.encodeEmailAddress=function(e){var a=[function(e){return"&#"+e.charCodeAt(0)+";"},function(e){return"&#x"+e.charCodeAt(0).toString(16)+";"},function(e){return e}];return e=e.replace(/./g,function(e){if("@"===e)e=a[Math.floor(2*Math.random())](e);else{var r=Math.random();e=.9<r?a[2](e):.45<r?a[1](e):a[0](e)}return e})},w.helper.padEnd=function(e,r,a){return r>>=0,a=String(a||" "),e.length>r?String(e):((r-=e.length)>a.length&&(a+=a.repeat(r/a.length)),String(e)+a.slice(0,r))},"undefined"==typeof console&&(console={warn:function(e){alert(e)},log:function(e){alert(e)},error:function(e){throw e}}),w.helper.regexes={asteriskDashAndColon:/([*_:~])/g},w.helper.emojis={"+1":"\ud83d\udc4d","-1":"\ud83d\udc4e",100:"\ud83d\udcaf",1234:"\ud83d\udd22","1st_place_medal":"\ud83e\udd47","2nd_place_medal":"\ud83e\udd48","3rd_place_medal":"\ud83e\udd49","8ball":"\ud83c\udfb1",a:"\ud83c\udd70\ufe0f",ab:"\ud83c\udd8e",abc:"\ud83d\udd24",abcd:"\ud83d\udd21",accept:"\ud83c\ude51",aerial_tramway:"\ud83d\udea1",airplane:"\u2708\ufe0f",alarm_clock:"\u23f0",alembic:"\u2697\ufe0f",alien:"\ud83d\udc7d",ambulance:"\ud83d\ude91",amphora:"\ud83c\udffa",anchor:"\u2693\ufe0f",angel:"\ud83d\udc7c",anger:"\ud83d\udca2",angry:"\ud83d\ude20",anguished:"\ud83d\ude27",ant:"\ud83d\udc1c",apple:"\ud83c\udf4e",aquarius:"\u2652\ufe0f",aries:"\u2648\ufe0f",arrow_backward:"\u25c0\ufe0f",arrow_double_down:"\u23ec",arrow_double_up:"\u23eb",arrow_down:"\u2b07\ufe0f",arrow_down_small:"\ud83d\udd3d",arrow_forward:"\u25b6\ufe0f",arrow_heading_down:"\u2935\ufe0f",arrow_heading_up:"\u2934\ufe0f",arrow_left:"\u2b05\ufe0f",arrow_lower_left:"\u2199\ufe0f",arrow_lower_right:"\u2198\ufe0f",arrow_right:"\u27a1\ufe0f",arrow_right_hook:"\u21aa\ufe0f",arrow_up:"\u2b06\ufe0f",arrow_up_down:"\u2195\ufe0f",arrow_up_small:"\ud83d\udd3c",arrow_upper_left:"\u2196\ufe0f",arrow_upper_right:"\u2197\ufe0f",arrows_clockwise:"\ud83d\udd03",arrows_counterclockwise:"\ud83d\udd04",art:"\ud83c\udfa8",articulated_lorry:"\ud83d\ude9b",artificial_satellite:"\ud83d\udef0",astonished:"\ud83d\ude32",athletic_shoe:"\ud83d\udc5f",atm:"\ud83c\udfe7",atom_symbol:"\u269b\ufe0f",avocado:"\ud83e\udd51",b:"\ud83c\udd71\ufe0f",baby:"\ud83d\udc76",baby_bottle:"\ud83c\udf7c",baby_chick:"\ud83d\udc24",baby_symbol:"\ud83d\udebc",back:"\ud83d\udd19",bacon:"\ud83e\udd53",badminton:"\ud83c\udff8",baggage_claim:"\ud83d\udec4",baguette_bread:"\ud83e\udd56",balance_scale:"\u2696\ufe0f",balloon:"\ud83c\udf88",ballot_box:"\ud83d\uddf3",ballot_box_with_check:"\u2611\ufe0f",bamboo:"\ud83c\udf8d",banana:"\ud83c\udf4c",bangbang:"\u203c\ufe0f",bank:"\ud83c\udfe6",bar_chart:"\ud83d\udcca",barber:"\ud83d\udc88",baseball:"\u26be\ufe0f",basketball:"\ud83c\udfc0",basketball_man:"\u26f9\ufe0f",basketball_woman:"\u26f9\ufe0f&zwj;\u2640\ufe0f",bat:"\ud83e\udd87",bath:"\ud83d\udec0",bathtub:"\ud83d\udec1",battery:"\ud83d\udd0b",beach_umbrella:"\ud83c\udfd6",bear:"\ud83d\udc3b",bed:"\ud83d\udecf",bee:"\ud83d\udc1d",beer:"\ud83c\udf7a",beers:"\ud83c\udf7b",beetle:"\ud83d\udc1e",beginner:"\ud83d\udd30",bell:"\ud83d\udd14",bellhop_bell:"\ud83d\udece",bento:"\ud83c\udf71",biking_man:"\ud83d\udeb4",bike:"\ud83d\udeb2",biking_woman:"\ud83d\udeb4&zwj;\u2640\ufe0f",bikini:"\ud83d\udc59",biohazard:"\u2623\ufe0f",bird:"\ud83d\udc26",birthday:"\ud83c\udf82",black_circle:"\u26ab\ufe0f",black_flag:"\ud83c\udff4",black_heart:"\ud83d\udda4",black_joker:"\ud83c\udccf",black_large_square:"\u2b1b\ufe0f",black_medium_small_square:"\u25fe\ufe0f",black_medium_square:"\u25fc\ufe0f",black_nib:"\u2712\ufe0f",black_small_square:"\u25aa\ufe0f",black_square_button:"\ud83d\udd32",blonde_man:"\ud83d\udc71",blonde_woman:"\ud83d\udc71&zwj;\u2640\ufe0f",blossom:"\ud83c\udf3c",blowfish:"\ud83d\udc21",blue_book:"\ud83d\udcd8",blue_car:"\ud83d\ude99",blue_heart:"\ud83d\udc99",blush:"\ud83d\ude0a",boar:"\ud83d\udc17",boat:"\u26f5\ufe0f",bomb:"\ud83d\udca3",book:"\ud83d\udcd6",bookmark:"\ud83d\udd16",bookmark_tabs:"\ud83d\udcd1",books:"\ud83d\udcda",boom:"\ud83d\udca5",boot:"\ud83d\udc62",bouquet:"\ud83d\udc90",bowing_man:"\ud83d\ude47",bow_and_arrow:"\ud83c\udff9",bowing_woman:"\ud83d\ude47&zwj;\u2640\ufe0f",bowling:"\ud83c\udfb3",boxing_glove:"\ud83e\udd4a",boy:"\ud83d\udc66",bread:"\ud83c\udf5e",bride_with_veil:"\ud83d\udc70",bridge_at_night:"\ud83c\udf09",briefcase:"\ud83d\udcbc",broken_heart:"\ud83d\udc94",bug:"\ud83d\udc1b",building_construction:"\ud83c\udfd7",bulb:"\ud83d\udca1",bullettrain_front:"\ud83d\ude85",bullettrain_side:"\ud83d\ude84",burrito:"\ud83c\udf2f",bus:"\ud83d\ude8c",business_suit_levitating:"\ud83d\udd74",busstop:"\ud83d\ude8f",bust_in_silhouette:"\ud83d\udc64",busts_in_silhouette:"\ud83d\udc65",butterfly:"\ud83e\udd8b",cactus:"\ud83c\udf35",cake:"\ud83c\udf70",calendar:"\ud83d\udcc6",call_me_hand:"\ud83e\udd19",calling:"\ud83d\udcf2",camel:"\ud83d\udc2b",camera:"\ud83d\udcf7",camera_flash:"\ud83d\udcf8",camping:"\ud83c\udfd5",cancer:"\u264b\ufe0f",candle:"\ud83d\udd6f",candy:"\ud83c\udf6c",canoe:"\ud83d\udef6",capital_abcd:"\ud83d\udd20",capricorn:"\u2651\ufe0f",car:"\ud83d\ude97",card_file_box:"\ud83d\uddc3",card_index:"\ud83d\udcc7",card_index_dividers:"\ud83d\uddc2",carousel_horse:"\ud83c\udfa0",carrot:"\ud83e\udd55",cat:"\ud83d\udc31",cat2:"\ud83d\udc08",cd:"\ud83d\udcbf",chains:"\u26d3",champagne:"\ud83c\udf7e",chart:"\ud83d\udcb9",chart_with_downwards_trend:"\ud83d\udcc9",chart_with_upwards_trend:"\ud83d\udcc8",checkered_flag:"\ud83c\udfc1",cheese:"\ud83e\uddc0",cherries:"\ud83c\udf52",cherry_blossom:"\ud83c\udf38",chestnut:"\ud83c\udf30",chicken:"\ud83d\udc14",children_crossing:"\ud83d\udeb8",chipmunk:"\ud83d\udc3f",chocolate_bar:"\ud83c\udf6b",christmas_tree:"\ud83c\udf84",church:"\u26ea\ufe0f",cinema:"\ud83c\udfa6",circus_tent:"\ud83c\udfaa",city_sunrise:"\ud83c\udf07",city_sunset:"\ud83c\udf06",cityscape:"\ud83c\udfd9",cl:"\ud83c\udd91",clamp:"\ud83d\udddc",clap:"\ud83d\udc4f",clapper:"\ud83c\udfac",classical_building:"\ud83c\udfdb",clinking_glasses:"\ud83e\udd42",clipboard:"\ud83d\udccb",clock1:"\ud83d\udd50",clock10:"\ud83d\udd59",clock1030:"\ud83d\udd65",clock11:"\ud83d\udd5a",clock1130:"\ud83d\udd66",clock12:"\ud83d\udd5b",clock1230:"\ud83d\udd67",clock130:"\ud83d\udd5c",clock2:"\ud83d\udd51",clock230:"\ud83d\udd5d",clock3:"\ud83d\udd52",clock330:"\ud83d\udd5e",clock4:"\ud83d\udd53",clock430:"\ud83d\udd5f",clock5:"\ud83d\udd54",clock530:"\ud83d\udd60",clock6:"\ud83d\udd55",clock630:"\ud83d\udd61",clock7:"\ud83d\udd56",clock730:"\ud83d\udd62",clock8:"\ud83d\udd57",clock830:"\ud83d\udd63",clock9:"\ud83d\udd58",clock930:"\ud83d\udd64",closed_book:"\ud83d\udcd5",closed_lock_with_key:"\ud83d\udd10",closed_umbrella:"\ud83c\udf02",cloud:"\u2601\ufe0f",cloud_with_lightning:"\ud83c\udf29",cloud_with_lightning_and_rain:"\u26c8",cloud_with_rain:"\ud83c\udf27",cloud_with_snow:"\ud83c\udf28",clown_face:"\ud83e\udd21",clubs:"\u2663\ufe0f",cocktail:"\ud83c\udf78",coffee:"\u2615\ufe0f",coffin:"\u26b0\ufe0f",cold_sweat:"\ud83d\ude30",comet:"\u2604\ufe0f",computer:"\ud83d\udcbb",computer_mouse:"\ud83d\uddb1",confetti_ball:"\ud83c\udf8a",confounded:"\ud83d\ude16",confused:"\ud83d\ude15",congratulations:"\u3297\ufe0f",construction:"\ud83d\udea7",construction_worker_man:"\ud83d\udc77",construction_worker_woman:"\ud83d\udc77&zwj;\u2640\ufe0f",control_knobs:"\ud83c\udf9b",convenience_store:"\ud83c\udfea",cookie:"\ud83c\udf6a",cool:"\ud83c\udd92",policeman:"\ud83d\udc6e",copyright:"\xa9\ufe0f",corn:"\ud83c\udf3d",couch_and_lamp:"\ud83d\udecb",couple:"\ud83d\udc6b",couple_with_heart_woman_man:"\ud83d\udc91",couple_with_heart_man_man:"\ud83d\udc68&zwj;\u2764\ufe0f&zwj;\ud83d\udc68",couple_with_heart_woman_woman:"\ud83d\udc69&zwj;\u2764\ufe0f&zwj;\ud83d\udc69",couplekiss_man_man:"\ud83d\udc68&zwj;\u2764\ufe0f&zwj;\ud83d\udc8b&zwj;\ud83d\udc68",couplekiss_man_woman:"\ud83d\udc8f",couplekiss_woman_woman:"\ud83d\udc69&zwj;\u2764\ufe0f&zwj;\ud83d\udc8b&zwj;\ud83d\udc69",cow:"\ud83d\udc2e",cow2:"\ud83d\udc04",cowboy_hat_face:"\ud83e\udd20",crab:"\ud83e\udd80",crayon:"\ud83d\udd8d",credit_card:"\ud83d\udcb3",crescent_moon:"\ud83c\udf19",cricket:"\ud83c\udfcf",crocodile:"\ud83d\udc0a",croissant:"\ud83e\udd50",crossed_fingers:"\ud83e\udd1e",crossed_flags:"\ud83c\udf8c",crossed_swords:"\u2694\ufe0f",crown:"\ud83d\udc51",cry:"\ud83d\ude22",crying_cat_face:"\ud83d\ude3f",crystal_ball:"\ud83d\udd2e",cucumber:"\ud83e\udd52",cupid:"\ud83d\udc98",curly_loop:"\u27b0",currency_exchange:"\ud83d\udcb1",curry:"\ud83c\udf5b",custard:"\ud83c\udf6e",customs:"\ud83d\udec3",cyclone:"\ud83c\udf00",dagger:"\ud83d\udde1",dancer:"\ud83d\udc83",dancing_women:"\ud83d\udc6f",dancing_men:"\ud83d\udc6f&zwj;\u2642\ufe0f",dango:"\ud83c\udf61",dark_sunglasses:"\ud83d\udd76",dart:"\ud83c\udfaf",dash:"\ud83d\udca8",date:"\ud83d\udcc5",deciduous_tree:"\ud83c\udf33",deer:"\ud83e\udd8c",department_store:"\ud83c\udfec",derelict_house:"\ud83c\udfda",desert:"\ud83c\udfdc",desert_island:"\ud83c\udfdd",desktop_computer:"\ud83d\udda5",male_detective:"\ud83d\udd75\ufe0f",diamond_shape_with_a_dot_inside:"\ud83d\udca0",diamonds:"\u2666\ufe0f",disappointed:"\ud83d\ude1e",disappointed_relieved:"\ud83d\ude25",dizzy:"\ud83d\udcab",dizzy_face:"\ud83d\ude35",do_not_litter:"\ud83d\udeaf",dog:"\ud83d\udc36",dog2:"\ud83d\udc15",dollar:"\ud83d\udcb5",dolls:"\ud83c\udf8e",dolphin:"\ud83d\udc2c",door:"\ud83d\udeaa",doughnut:"\ud83c\udf69",dove:"\ud83d\udd4a",dragon:"\ud83d\udc09",dragon_face:"\ud83d\udc32",dress:"\ud83d\udc57",dromedary_camel:"\ud83d\udc2a",drooling_face:"\ud83e\udd24",droplet:"\ud83d\udca7",drum:"\ud83e\udd41",duck:"\ud83e\udd86",dvd:"\ud83d\udcc0","e-mail":"\ud83d\udce7",eagle:"\ud83e\udd85",ear:"\ud83d\udc42",ear_of_rice:"\ud83c\udf3e",earth_africa:"\ud83c\udf0d",earth_americas:"\ud83c\udf0e",earth_asia:"\ud83c\udf0f",egg:"\ud83e\udd5a",eggplant:"\ud83c\udf46",eight_pointed_black_star:"\u2734\ufe0f",eight_spoked_asterisk:"\u2733\ufe0f",electric_plug:"\ud83d\udd0c",elephant:"\ud83d\udc18",email:"\u2709\ufe0f",end:"\ud83d\udd1a",envelope_with_arrow:"\ud83d\udce9",euro:"\ud83d\udcb6",european_castle:"\ud83c\udff0",european_post_office:"\ud83c\udfe4",evergreen_tree:"\ud83c\udf32",exclamation:"\u2757\ufe0f",expressionless:"\ud83d\ude11",eye:"\ud83d\udc41",eye_speech_bubble:"\ud83d\udc41&zwj;\ud83d\udde8",eyeglasses:"\ud83d\udc53",eyes:"\ud83d\udc40",face_with_head_bandage:"\ud83e\udd15",face_with_thermometer:"\ud83e\udd12",fist_oncoming:"\ud83d\udc4a",factory:"\ud83c\udfed",fallen_leaf:"\ud83c\udf42",family_man_woman_boy:"\ud83d\udc6a",family_man_boy:"\ud83d\udc68&zwj;\ud83d\udc66",family_man_boy_boy:"\ud83d\udc68&zwj;\ud83d\udc66&zwj;\ud83d\udc66",family_man_girl:"\ud83d\udc68&zwj;\ud83d\udc67",family_man_girl_boy:"\ud83d\udc68&zwj;\ud83d\udc67&zwj;\ud83d\udc66",family_man_girl_girl:"\ud83d\udc68&zwj;\ud83d\udc67&zwj;\ud83d\udc67",family_man_man_boy:"\ud83d\udc68&zwj;\ud83d\udc68&zwj;\ud83d\udc66",family_man_man_boy_boy:"\ud83d\udc68&zwj;\ud83d\udc68&zwj;\ud83d\udc66&zwj;\ud83d\udc66",family_man_man_girl:"\ud83d\udc68&zwj;\ud83d\udc68&zwj;\ud83d\udc67",family_man_man_girl_boy:"\ud83d\udc68&zwj;\ud83d\udc68&zwj;\ud83d\udc67&zwj;\ud83d\udc66",family_man_man_girl_girl:"\ud83d\udc68&zwj;\ud83d\udc68&zwj;\ud83d\udc67&zwj;\ud83d\udc67",family_man_woman_boy_boy:"\ud83d\udc68&zwj;\ud83d\udc69&zwj;\ud83d\udc66&zwj;\ud83d\udc66",family_man_woman_girl:"\ud83d\udc68&zwj;\ud83d\udc69&zwj;\ud83d\udc67",family_man_woman_girl_boy:"\ud83d\udc68&zwj;\ud83d\udc69&zwj;\ud83d\udc67&zwj;\ud83d\udc66",family_man_woman_girl_girl:"\ud83d\udc68&zwj;\ud83d\udc69&zwj;\ud83d\udc67&zwj;\ud83d\udc67",family_woman_boy:"\ud83d\udc69&zwj;\ud83d\udc66",family_woman_boy_boy:"\ud83d\udc69&zwj;\ud83d\udc66&zwj;\ud83d\udc66",family_woman_girl:"\ud83d\udc69&zwj;\ud83d\udc67",family_woman_girl_boy:"\ud83d\udc69&zwj;\ud83d\udc67&zwj;\ud83d\udc66",family_woman_girl_girl:"\ud83d\udc69&zwj;\ud83d\udc67&zwj;\ud83d\udc67",family_woman_woman_boy:"\ud83d\udc69&zwj;\ud83d\udc69&zwj;\ud83d\udc66",family_woman_woman_boy_boy:"\ud83d\udc69&zwj;\ud83d\udc69&zwj;\ud83d\udc66&zwj;\ud83d\udc66",family_woman_woman_girl:"\ud83d\udc69&zwj;\ud83d\udc69&zwj;\ud83d\udc67",family_woman_woman_girl_boy:"\ud83d\udc69&zwj;\ud83d\udc69&zwj;\ud83d\udc67&zwj;\ud83d\udc66",family_woman_woman_girl_girl:"\ud83d\udc69&zwj;\ud83d\udc69&zwj;\ud83d\udc67&zwj;\ud83d\udc67",fast_forward:"\u23e9",fax:"\ud83d\udce0",fearful:"\ud83d\ude28",feet:"\ud83d\udc3e",female_detective:"\ud83d\udd75\ufe0f&zwj;\u2640\ufe0f",ferris_wheel:"\ud83c\udfa1",ferry:"\u26f4",field_hockey:"\ud83c\udfd1",file_cabinet:"\ud83d\uddc4",file_folder:"\ud83d\udcc1",film_projector:"\ud83d\udcfd",film_strip:"\ud83c\udf9e",fire:"\ud83d\udd25",fire_engine:"\ud83d\ude92",fireworks:"\ud83c\udf86",first_quarter_moon:"\ud83c\udf13",first_quarter_moon_with_face:"\ud83c\udf1b",fish:"\ud83d\udc1f",fish_cake:"\ud83c\udf65",fishing_pole_and_fish:"\ud83c\udfa3",fist_raised:"\u270a",fist_left:"\ud83e\udd1b",fist_right:"\ud83e\udd1c",flags:"\ud83c\udf8f",flashlight:"\ud83d\udd26",fleur_de_lis:"\u269c\ufe0f",flight_arrival:"\ud83d\udeec",flight_departure:"\ud83d\udeeb",floppy_disk:"\ud83d\udcbe",flower_playing_cards:"\ud83c\udfb4",flushed:"\ud83d\ude33",fog:"\ud83c\udf2b",foggy:"\ud83c\udf01",football:"\ud83c\udfc8",footprints:"\ud83d\udc63",fork_and_knife:"\ud83c\udf74",fountain:"\u26f2\ufe0f",fountain_pen:"\ud83d\udd8b",four_leaf_clover:"\ud83c\udf40",fox_face:"\ud83e\udd8a",framed_picture:"\ud83d\uddbc",free:"\ud83c\udd93",fried_egg:"\ud83c\udf73",fried_shrimp:"\ud83c\udf64",fries:"\ud83c\udf5f",frog:"\ud83d\udc38",frowning:"\ud83d\ude26",frowning_face:"\u2639\ufe0f",frowning_man:"\ud83d\ude4d&zwj;\u2642\ufe0f",frowning_woman:"\ud83d\ude4d",middle_finger:"\ud83d\udd95",fuelpump:"\u26fd\ufe0f",full_moon:"\ud83c\udf15",full_moon_with_face:"\ud83c\udf1d",funeral_urn:"\u26b1\ufe0f",game_die:"\ud83c\udfb2",gear:"\u2699\ufe0f",gem:"\ud83d\udc8e",gemini:"\u264a\ufe0f",ghost:"\ud83d\udc7b",gift:"\ud83c\udf81",gift_heart:"\ud83d\udc9d",girl:"\ud83d\udc67",globe_with_meridians:"\ud83c\udf10",goal_net:"\ud83e\udd45",goat:"\ud83d\udc10",golf:"\u26f3\ufe0f",golfing_man:"\ud83c\udfcc\ufe0f",golfing_woman:"\ud83c\udfcc\ufe0f&zwj;\u2640\ufe0f",gorilla:"\ud83e\udd8d",grapes:"\ud83c\udf47",green_apple:"\ud83c\udf4f",green_book:"\ud83d\udcd7",green_heart:"\ud83d\udc9a",green_salad:"\ud83e\udd57",grey_exclamation:"\u2755",grey_question:"\u2754",grimacing:"\ud83d\ude2c",grin:"\ud83d\ude01",grinning:"\ud83d\ude00",guardsman:"\ud83d\udc82",guardswoman:"\ud83d\udc82&zwj;\u2640\ufe0f",guitar:"\ud83c\udfb8",gun:"\ud83d\udd2b",haircut_woman:"\ud83d\udc87",haircut_man:"\ud83d\udc87&zwj;\u2642\ufe0f",hamburger:"\ud83c\udf54",hammer:"\ud83d\udd28",hammer_and_pick:"\u2692",hammer_and_wrench:"\ud83d\udee0",hamster:"\ud83d\udc39",hand:"\u270b",handbag:"\ud83d\udc5c",handshake:"\ud83e\udd1d",hankey:"\ud83d\udca9",hatched_chick:"\ud83d\udc25",hatching_chick:"\ud83d\udc23",headphones:"\ud83c\udfa7",hear_no_evil:"\ud83d\ude49",heart:"\u2764\ufe0f",heart_decoration:"\ud83d\udc9f",heart_eyes:"\ud83d\ude0d",heart_eyes_cat:"\ud83d\ude3b",heartbeat:"\ud83d\udc93",heartpulse:"\ud83d\udc97",hearts:"\u2665\ufe0f",heavy_check_mark:"\u2714\ufe0f",heavy_division_sign:"\u2797",heavy_dollar_sign:"\ud83d\udcb2",heavy_heart_exclamation:"\u2763\ufe0f",heavy_minus_sign:"\u2796",heavy_multiplication_x:"\u2716\ufe0f",heavy_plus_sign:"\u2795",helicopter:"\ud83d\ude81",herb:"\ud83c\udf3f",hibiscus:"\ud83c\udf3a",high_brightness:"\ud83d\udd06",high_heel:"\ud83d\udc60",hocho:"\ud83d\udd2a",hole:"\ud83d\udd73",honey_pot:"\ud83c\udf6f",horse:"\ud83d\udc34",horse_racing:"\ud83c\udfc7",hospital:"\ud83c\udfe5",hot_pepper:"\ud83c\udf36",hotdog:"\ud83c\udf2d",hotel:"\ud83c\udfe8",hotsprings:"\u2668\ufe0f",hourglass:"\u231b\ufe0f",hourglass_flowing_sand:"\u23f3",house:"\ud83c\udfe0",house_with_garden:"\ud83c\udfe1",houses:"\ud83c\udfd8",hugs:"\ud83e\udd17",hushed:"\ud83d\ude2f",ice_cream:"\ud83c\udf68",ice_hockey:"\ud83c\udfd2",ice_skate:"\u26f8",icecream:"\ud83c\udf66",id:"\ud83c\udd94",ideograph_advantage:"\ud83c\ude50",imp:"\ud83d\udc7f",inbox_tray:"\ud83d\udce5",incoming_envelope:"\ud83d\udce8",tipping_hand_woman:"\ud83d\udc81",information_source:"\u2139\ufe0f",innocent:"\ud83d\ude07",interrobang:"\u2049\ufe0f",iphone:"\ud83d\udcf1",izakaya_lantern:"\ud83c\udfee",jack_o_lantern:"\ud83c\udf83",japan:"\ud83d\uddfe",japanese_castle:"\ud83c\udfef",japanese_goblin:"\ud83d\udc7a",japanese_ogre:"\ud83d\udc79",jeans:"\ud83d\udc56",joy:"\ud83d\ude02",joy_cat:"\ud83d\ude39",joystick:"\ud83d\udd79",kaaba:"\ud83d\udd4b",key:"\ud83d\udd11",keyboard:"\u2328\ufe0f",keycap_ten:"\ud83d\udd1f",kick_scooter:"\ud83d\udef4",kimono:"\ud83d\udc58",kiss:"\ud83d\udc8b",kissing:"\ud83d\ude17",kissing_cat:"\ud83d\ude3d",kissing_closed_eyes:"\ud83d\ude1a",kissing_heart:"\ud83d\ude18",kissing_smiling_eyes:"\ud83d\ude19",kiwi_fruit:"\ud83e\udd5d",koala:"\ud83d\udc28",koko:"\ud83c\ude01",label:"\ud83c\udff7",large_blue_circle:"\ud83d\udd35",large_blue_diamond:"\ud83d\udd37",large_orange_diamond:"\ud83d\udd36",last_quarter_moon:"\ud83c\udf17",last_quarter_moon_with_face:"\ud83c\udf1c",latin_cross:"\u271d\ufe0f",laughing:"\ud83d\ude06",leaves:"\ud83c\udf43",ledger:"\ud83d\udcd2",left_luggage:"\ud83d\udec5",left_right_arrow:"\u2194\ufe0f",leftwards_arrow_with_hook:"\u21a9\ufe0f",lemon:"\ud83c\udf4b",leo:"\u264c\ufe0f",leopard:"\ud83d\udc06",level_slider:"\ud83c\udf9a",libra:"\u264e\ufe0f",light_rail:"\ud83d\ude88",link:"\ud83d\udd17",lion:"\ud83e\udd81",lips:"\ud83d\udc44",lipstick:"\ud83d\udc84",lizard:"\ud83e\udd8e",lock:"\ud83d\udd12",lock_with_ink_pen:"\ud83d\udd0f",lollipop:"\ud83c\udf6d",loop:"\u27bf",loud_sound:"\ud83d\udd0a",loudspeaker:"\ud83d\udce2",love_hotel:"\ud83c\udfe9",love_letter:"\ud83d\udc8c",low_brightness:"\ud83d\udd05",lying_face:"\ud83e\udd25",m:"\u24c2\ufe0f",mag:"\ud83d\udd0d",mag_right:"\ud83d\udd0e",mahjong:"\ud83c\udc04\ufe0f",mailbox:"\ud83d\udceb",mailbox_closed:"\ud83d\udcea",mailbox_with_mail:"\ud83d\udcec",mailbox_with_no_mail:"\ud83d\udced",man:"\ud83d\udc68",man_artist:"\ud83d\udc68&zwj;\ud83c\udfa8",man_astronaut:"\ud83d\udc68&zwj;\ud83d\ude80",man_cartwheeling:"\ud83e\udd38&zwj;\u2642\ufe0f",man_cook:"\ud83d\udc68&zwj;\ud83c\udf73",man_dancing:"\ud83d\udd7a",man_facepalming:"\ud83e\udd26&zwj;\u2642\ufe0f",man_factory_worker:"\ud83d\udc68&zwj;\ud83c\udfed",man_farmer:"\ud83d\udc68&zwj;\ud83c\udf3e",man_firefighter:"\ud83d\udc68&zwj;\ud83d\ude92",man_health_worker:"\ud83d\udc68&zwj;\u2695\ufe0f",man_in_tuxedo:"\ud83e\udd35",man_judge:"\ud83d\udc68&zwj;\u2696\ufe0f",man_juggling:"\ud83e\udd39&zwj;\u2642\ufe0f",man_mechanic:"\ud83d\udc68&zwj;\ud83d\udd27",man_office_worker:"\ud83d\udc68&zwj;\ud83d\udcbc",man_pilot:"\ud83d\udc68&zwj;\u2708\ufe0f",man_playing_handball:"\ud83e\udd3e&zwj;\u2642\ufe0f",man_playing_water_polo:"\ud83e\udd3d&zwj;\u2642\ufe0f",man_scientist:"\ud83d\udc68&zwj;\ud83d\udd2c",man_shrugging:"\ud83e\udd37&zwj;\u2642\ufe0f",man_singer:"\ud83d\udc68&zwj;\ud83c\udfa4",man_student:"\ud83d\udc68&zwj;\ud83c\udf93",man_teacher:"\ud83d\udc68&zwj;\ud83c\udfeb",man_technologist:"\ud83d\udc68&zwj;\ud83d\udcbb",man_with_gua_pi_mao:"\ud83d\udc72",man_with_turban:"\ud83d\udc73",tangerine:"\ud83c\udf4a",mans_shoe:"\ud83d\udc5e",mantelpiece_clock:"\ud83d\udd70",maple_leaf:"\ud83c\udf41",martial_arts_uniform:"\ud83e\udd4b",mask:"\ud83d\ude37",massage_woman:"\ud83d\udc86",massage_man:"\ud83d\udc86&zwj;\u2642\ufe0f",meat_on_bone:"\ud83c\udf56",medal_military:"\ud83c\udf96",medal_sports:"\ud83c\udfc5",mega:"\ud83d\udce3",melon:"\ud83c\udf48",memo:"\ud83d\udcdd",men_wrestling:"\ud83e\udd3c&zwj;\u2642\ufe0f",menorah:"\ud83d\udd4e",mens:"\ud83d\udeb9",metal:"\ud83e\udd18",metro:"\ud83d\ude87",microphone:"\ud83c\udfa4",microscope:"\ud83d\udd2c",milk_glass:"\ud83e\udd5b",milky_way:"\ud83c\udf0c",minibus:"\ud83d\ude90",minidisc:"\ud83d\udcbd",mobile_phone_off:"\ud83d\udcf4",money_mouth_face:"\ud83e\udd11",money_with_wings:"\ud83d\udcb8",moneybag:"\ud83d\udcb0",monkey:"\ud83d\udc12",monkey_face:"\ud83d\udc35",monorail:"\ud83d\ude9d",moon:"\ud83c\udf14",mortar_board:"\ud83c\udf93",mosque:"\ud83d\udd4c",motor_boat:"\ud83d\udee5",motor_scooter:"\ud83d\udef5",motorcycle:"\ud83c\udfcd",motorway:"\ud83d\udee3",mount_fuji:"\ud83d\uddfb",mountain:"\u26f0",mountain_biking_man:"\ud83d\udeb5",mountain_biking_woman:"\ud83d\udeb5&zwj;\u2640\ufe0f",mountain_cableway:"\ud83d\udea0",mountain_railway:"\ud83d\ude9e",mountain_snow:"\ud83c\udfd4",mouse:"\ud83d\udc2d",mouse2:"\ud83d\udc01",movie_camera:"\ud83c\udfa5",moyai:"\ud83d\uddff",mrs_claus:"\ud83e\udd36",muscle:"\ud83d\udcaa",mushroom:"\ud83c\udf44",musical_keyboard:"\ud83c\udfb9",musical_note:"\ud83c\udfb5",musical_score:"\ud83c\udfbc",mute:"\ud83d\udd07",nail_care:"\ud83d\udc85",name_badge:"\ud83d\udcdb",national_park:"\ud83c\udfde",nauseated_face:"\ud83e\udd22",necktie:"\ud83d\udc54",negative_squared_cross_mark:"\u274e",nerd_face:"\ud83e\udd13",neutral_face:"\ud83d\ude10","new":"\ud83c\udd95",new_moon:"\ud83c\udf11",new_moon_with_face:"\ud83c\udf1a",newspaper:"\ud83d\udcf0",newspaper_roll:"\ud83d\uddde",next_track_button:"\u23ed",ng:"\ud83c\udd96",no_good_man:"\ud83d\ude45&zwj;\u2642\ufe0f",no_good_woman:"\ud83d\ude45",night_with_stars:"\ud83c\udf03",no_bell:"\ud83d\udd15",no_bicycles:"\ud83d\udeb3",no_entry:"\u26d4\ufe0f",no_entry_sign:"\ud83d\udeab",no_mobile_phones:"\ud83d\udcf5",no_mouth:"\ud83d\ude36",no_pedestrians:"\ud83d\udeb7",no_smoking:"\ud83d\udead","non-potable_water":"\ud83d\udeb1",nose:"\ud83d\udc43",notebook:"\ud83d\udcd3",notebook_with_decorative_cover:"\ud83d\udcd4",notes:"\ud83c\udfb6",nut_and_bolt:"\ud83d\udd29",o:"\u2b55\ufe0f",o2:"\ud83c\udd7e\ufe0f",ocean:"\ud83c\udf0a",octopus:"\ud83d\udc19",oden:"\ud83c\udf62",office:"\ud83c\udfe2",oil_drum:"\ud83d\udee2",ok:"\ud83c\udd97",ok_hand:"\ud83d\udc4c",ok_man:"\ud83d\ude46&zwj;\u2642\ufe0f",ok_woman:"\ud83d\ude46",old_key:"\ud83d\udddd",older_man:"\ud83d\udc74",older_woman:"\ud83d\udc75",om:"\ud83d\udd49",on:"\ud83d\udd1b",oncoming_automobile:"\ud83d\ude98",oncoming_bus:"\ud83d\ude8d",oncoming_police_car:"\ud83d\ude94",oncoming_taxi:"\ud83d\ude96",open_file_folder:"\ud83d\udcc2",open_hands:"\ud83d\udc50",open_mouth:"\ud83d\ude2e",open_umbrella:"\u2602\ufe0f",ophiuchus:"\u26ce",orange_book:"\ud83d\udcd9",orthodox_cross:"\u2626\ufe0f",outbox_tray:"\ud83d\udce4",owl:"\ud83e\udd89",ox:"\ud83d\udc02","package":"\ud83d\udce6",page_facing_up:"\ud83d\udcc4",page_with_curl:"\ud83d\udcc3",pager:"\ud83d\udcdf",paintbrush:"\ud83d\udd8c",palm_tree:"\ud83c\udf34",pancakes:"\ud83e\udd5e",panda_face:"\ud83d\udc3c",paperclip:"\ud83d\udcce",paperclips:"\ud83d\udd87",parasol_on_ground:"\u26f1",parking:"\ud83c\udd7f\ufe0f",part_alternation_mark:"\u303d\ufe0f",partly_sunny:"\u26c5\ufe0f",passenger_ship:"\ud83d\udef3",passport_control:"\ud83d\udec2",pause_button:"\u23f8",peace_symbol:"\u262e\ufe0f",peach:"\ud83c\udf51",peanuts:"\ud83e\udd5c",pear:"\ud83c\udf50",pen:"\ud83d\udd8a",pencil2:"\u270f\ufe0f",penguin:"\ud83d\udc27",pensive:"\ud83d\ude14",performing_arts:"\ud83c\udfad",persevere:"\ud83d\ude23",person_fencing:"\ud83e\udd3a",pouting_woman:"\ud83d\ude4e",phone:"\u260e\ufe0f",pick:"\u26cf",pig:"\ud83d\udc37",pig2:"\ud83d\udc16",pig_nose:"\ud83d\udc3d",pill:"\ud83d\udc8a",pineapple:"\ud83c\udf4d",ping_pong:"\ud83c\udfd3",pisces:"\u2653\ufe0f",pizza:"\ud83c\udf55",place_of_worship:"\ud83d\uded0",plate_with_cutlery:"\ud83c\udf7d",play_or_pause_button:"\u23ef",point_down:"\ud83d\udc47",point_left:"\ud83d\udc48",point_right:"\ud83d\udc49",point_up:"\u261d\ufe0f",point_up_2:"\ud83d\udc46",police_car:"\ud83d\ude93",policewoman:"\ud83d\udc6e&zwj;\u2640\ufe0f",poodle:"\ud83d\udc29",popcorn:"\ud83c\udf7f",post_office:"\ud83c\udfe3",postal_horn:"\ud83d\udcef",postbox:"\ud83d\udcee",potable_water:"\ud83d\udeb0",potato:"\ud83e\udd54",pouch:"\ud83d\udc5d",poultry_leg:"\ud83c\udf57",pound:"\ud83d\udcb7",rage:"\ud83d\ude21",pouting_cat:"\ud83d\ude3e",pouting_man:"\ud83d\ude4e&zwj;\u2642\ufe0f",pray:"\ud83d\ude4f",prayer_beads:"\ud83d\udcff",pregnant_woman:"\ud83e\udd30",previous_track_button:"\u23ee",prince:"\ud83e\udd34",princess:"\ud83d\udc78",printer:"\ud83d\udda8",purple_heart:"\ud83d\udc9c",purse:"\ud83d\udc5b",pushpin:"\ud83d\udccc",put_litter_in_its_place:"\ud83d\udeae",question:"\u2753",rabbit:"\ud83d\udc30",rabbit2:"\ud83d\udc07",racehorse:"\ud83d\udc0e",racing_car:"\ud83c\udfce",radio:"\ud83d\udcfb",radio_button:"\ud83d\udd18",radioactive:"\u2622\ufe0f",railway_car:"\ud83d\ude83",railway_track:"\ud83d\udee4",rainbow:"\ud83c\udf08",rainbow_flag:"\ud83c\udff3\ufe0f&zwj;\ud83c\udf08",raised_back_of_hand:"\ud83e\udd1a",raised_hand_with_fingers_splayed:"\ud83d\udd90",raised_hands:"\ud83d\ude4c",raising_hand_woman:"\ud83d\ude4b",raising_hand_man:"\ud83d\ude4b&zwj;\u2642\ufe0f",ram:"\ud83d\udc0f",ramen:"\ud83c\udf5c",rat:"\ud83d\udc00",record_button:"\u23fa",recycle:"\u267b\ufe0f",red_circle:"\ud83d\udd34",registered:"\xae\ufe0f",relaxed:"\u263a\ufe0f",relieved:"\ud83d\ude0c",reminder_ribbon:"\ud83c\udf97",repeat:"\ud83d\udd01",repeat_one:"\ud83d\udd02",rescue_worker_helmet:"\u26d1",restroom:"\ud83d\udebb",revolving_hearts:"\ud83d\udc9e",rewind:"\u23ea",rhinoceros:"\ud83e\udd8f",ribbon:"\ud83c\udf80",rice:"\ud83c\udf5a",rice_ball:"\ud83c\udf59",rice_cracker:"\ud83c\udf58",rice_scene:"\ud83c\udf91",right_anger_bubble:"\ud83d\uddef",ring:"\ud83d\udc8d",robot:"\ud83e\udd16",rocket:"\ud83d\ude80",rofl:"\ud83e\udd23",roll_eyes:"\ud83d\ude44",roller_coaster:"\ud83c\udfa2",rooster:"\ud83d\udc13",rose:"\ud83c\udf39",rosette:"\ud83c\udff5",rotating_light:"\ud83d\udea8",round_pushpin:"\ud83d\udccd",rowing_man:"\ud83d\udea3",rowing_woman:"\ud83d\udea3&zwj;\u2640\ufe0f",rugby_football:"\ud83c\udfc9",running_man:"\ud83c\udfc3",running_shirt_with_sash:"\ud83c\udfbd",running_woman:"\ud83c\udfc3&zwj;\u2640\ufe0f",sa:"\ud83c\ude02\ufe0f",sagittarius:"\u2650\ufe0f",sake:"\ud83c\udf76",sandal:"\ud83d\udc61",santa:"\ud83c\udf85",satellite:"\ud83d\udce1",saxophone:"\ud83c\udfb7",school:"\ud83c\udfeb",school_satchel:"\ud83c\udf92",scissors:"\u2702\ufe0f",scorpion:"\ud83e\udd82",scorpius:"\u264f\ufe0f",scream:"\ud83d\ude31",scream_cat:"\ud83d\ude40",scroll:"\ud83d\udcdc",seat:"\ud83d\udcba",secret:"\u3299\ufe0f",see_no_evil:"\ud83d\ude48",seedling:"\ud83c\udf31",selfie:"\ud83e\udd33",shallow_pan_of_food:"\ud83e\udd58",shamrock:"\u2618\ufe0f",shark:"\ud83e\udd88",shaved_ice:"\ud83c\udf67",sheep:"\ud83d\udc11",shell:"\ud83d\udc1a",shield:"\ud83d\udee1",shinto_shrine:"\u26e9",ship:"\ud83d\udea2",shirt:"\ud83d\udc55",shopping:"\ud83d\udecd",shopping_cart:"\ud83d\uded2",shower:"\ud83d\udebf",shrimp:"\ud83e\udd90",signal_strength:"\ud83d\udcf6",six_pointed_star:"\ud83d\udd2f",ski:"\ud83c\udfbf",skier:"\u26f7",skull:"\ud83d\udc80",skull_and_crossbones:"\u2620\ufe0f",sleeping:"\ud83d\ude34",sleeping_bed:"\ud83d\udecc",sleepy:"\ud83d\ude2a",slightly_frowning_face:"\ud83d\ude41",slightly_smiling_face:"\ud83d\ude42",slot_machine:"\ud83c\udfb0",small_airplane:"\ud83d\udee9",small_blue_diamond:"\ud83d\udd39",small_orange_diamond:"\ud83d\udd38",small_red_triangle:"\ud83d\udd3a",small_red_triangle_down:"\ud83d\udd3b",smile:"\ud83d\ude04",smile_cat:"\ud83d\ude38",smiley:"\ud83d\ude03",smiley_cat:"\ud83d\ude3a",smiling_imp:"\ud83d\ude08",smirk:"\ud83d\ude0f",smirk_cat:"\ud83d\ude3c",smoking:"\ud83d\udeac",snail:"\ud83d\udc0c",snake:"\ud83d\udc0d",sneezing_face:"\ud83e\udd27",snowboarder:"\ud83c\udfc2",snowflake:"\u2744\ufe0f",snowman:"\u26c4\ufe0f",snowman_with_snow:"\u2603\ufe0f",sob:"\ud83d\ude2d",soccer:"\u26bd\ufe0f",soon:"\ud83d\udd1c",sos:"\ud83c\udd98",sound:"\ud83d\udd09",space_invader:"\ud83d\udc7e",spades:"\u2660\ufe0f",spaghetti:"\ud83c\udf5d",sparkle:"\u2747\ufe0f",sparkler:"\ud83c\udf87",sparkles:"\u2728",sparkling_heart:"\ud83d\udc96",speak_no_evil:"\ud83d\ude4a",speaker:"\ud83d\udd08",speaking_head:"\ud83d\udde3",speech_balloon:"\ud83d\udcac",speedboat:"\ud83d\udea4",spider:"\ud83d\udd77",spider_web:"\ud83d\udd78",spiral_calendar:"\ud83d\uddd3",spiral_notepad:"\ud83d\uddd2",spoon:"\ud83e\udd44",squid:"\ud83e\udd91",stadium:"\ud83c\udfdf",star:"\u2b50\ufe0f",star2:"\ud83c\udf1f",star_and_crescent:"\u262a\ufe0f",star_of_david:"\u2721\ufe0f",stars:"\ud83c\udf20",station:"\ud83d\ude89",statue_of_liberty:"\ud83d\uddfd",steam_locomotive:"\ud83d\ude82",stew:"\ud83c\udf72",stop_button:"\u23f9",stop_sign:"\ud83d\uded1",stopwatch:"\u23f1",straight_ruler:"\ud83d\udccf",strawberry:"\ud83c\udf53",stuck_out_tongue:"\ud83d\ude1b",stuck_out_tongue_closed_eyes:"\ud83d\ude1d",stuck_out_tongue_winking_eye:"\ud83d\ude1c",studio_microphone:"\ud83c\udf99",stuffed_flatbread:"\ud83e\udd59",sun_behind_large_cloud:"\ud83c\udf25",sun_behind_rain_cloud:"\ud83c\udf26",sun_behind_small_cloud:"\ud83c\udf24",sun_with_face:"\ud83c\udf1e",sunflower:"\ud83c\udf3b",sunglasses:"\ud83d\ude0e",sunny:"\u2600\ufe0f",sunrise:"\ud83c\udf05",sunrise_over_mountains:"\ud83c\udf04",surfing_man:"\ud83c\udfc4",surfing_woman:"\ud83c\udfc4&zwj;\u2640\ufe0f",sushi:"\ud83c\udf63",suspension_railway:"\ud83d\ude9f",sweat:"\ud83d\ude13",sweat_drops:"\ud83d\udca6",sweat_smile:"\ud83d\ude05",sweet_potato:"\ud83c\udf60",swimming_man:"\ud83c\udfca",swimming_woman:"\ud83c\udfca&zwj;\u2640\ufe0f",symbols:"\ud83d\udd23",synagogue:"\ud83d\udd4d",syringe:"\ud83d\udc89",taco:"\ud83c\udf2e",tada:"\ud83c\udf89",tanabata_tree:"\ud83c\udf8b",taurus:"\u2649\ufe0f",taxi:"\ud83d\ude95",tea:"\ud83c\udf75",telephone_receiver:"\ud83d\udcde",telescope:"\ud83d\udd2d",tennis:"\ud83c\udfbe",tent:"\u26fa\ufe0f",thermometer:"\ud83c\udf21",thinking:"\ud83e\udd14",thought_balloon:"\ud83d\udcad",ticket:"\ud83c\udfab",tickets:"\ud83c\udf9f",tiger:"\ud83d\udc2f",tiger2:"\ud83d\udc05",timer_clock:"\u23f2",tipping_hand_man:"\ud83d\udc81&zwj;\u2642\ufe0f",tired_face:"\ud83d\ude2b",tm:"\u2122\ufe0f",toilet:"\ud83d\udebd",tokyo_tower:"\ud83d\uddfc",tomato:"\ud83c\udf45",tongue:"\ud83d\udc45",top:"\ud83d\udd1d",tophat:"\ud83c\udfa9",tornado:"\ud83c\udf2a",trackball:"\ud83d\uddb2",tractor:"\ud83d\ude9c",traffic_light:"\ud83d\udea5",train:"\ud83d\ude8b",train2:"\ud83d\ude86",tram:"\ud83d\ude8a",triangular_flag_on_post:"\ud83d\udea9",triangular_ruler:"\ud83d\udcd0",trident:"\ud83d\udd31",triumph:"\ud83d\ude24",trolleybus:"\ud83d\ude8e",trophy:"\ud83c\udfc6",tropical_drink:"\ud83c\udf79",tropical_fish:"\ud83d\udc20",truck:"\ud83d\ude9a",trumpet:"\ud83c\udfba",tulip:"\ud83c\udf37",tumbler_glass:"\ud83e\udd43",turkey:"\ud83e\udd83",turtle:"\ud83d\udc22",tv:"\ud83d\udcfa",twisted_rightwards_arrows:"\ud83d\udd00",two_hearts:"\ud83d\udc95",two_men_holding_hands:"\ud83d\udc6c",two_women_holding_hands:"\ud83d\udc6d",u5272:"\ud83c\ude39",u5408:"\ud83c\ude34",u55b6:"\ud83c\ude3a",u6307:"\ud83c\ude2f\ufe0f",u6708:"\ud83c\ude37\ufe0f",u6709:"\ud83c\ude36",u6e80:"\ud83c\ude35",u7121:"\ud83c\ude1a\ufe0f",u7533:"\ud83c\ude38",u7981:"\ud83c\ude32",u7a7a:"\ud83c\ude33",umbrella:"\u2614\ufe0f",unamused:"\ud83d\ude12",underage:"\ud83d\udd1e",unicorn:"\ud83e\udd84",unlock:"\ud83d\udd13",up:"\ud83c\udd99",upside_down_face:"\ud83d\ude43",v:"\u270c\ufe0f",vertical_traffic_light:"\ud83d\udea6",vhs:"\ud83d\udcfc",vibration_mode:"\ud83d\udcf3",video_camera:"\ud83d\udcf9",video_game:"\ud83c\udfae",violin:"\ud83c\udfbb",virgo:"\u264d\ufe0f",volcano:"\ud83c\udf0b",volleyball:"\ud83c\udfd0",vs:"\ud83c\udd9a",vulcan_salute:"\ud83d\udd96",walking_man:"\ud83d\udeb6",walking_woman:"\ud83d\udeb6&zwj;\u2640\ufe0f",waning_crescent_moon:"\ud83c\udf18",waning_gibbous_moon:"\ud83c\udf16",warning:"\u26a0\ufe0f",wastebasket:"\ud83d\uddd1",watch:"\u231a\ufe0f",water_buffalo:"\ud83d\udc03",watermelon:"\ud83c\udf49",wave:"\ud83d\udc4b",wavy_dash:"\u3030\ufe0f",waxing_crescent_moon:"\ud83c\udf12",wc:"\ud83d\udebe",weary:"\ud83d\ude29",wedding:"\ud83d\udc92",weight_lifting_man:"\ud83c\udfcb\ufe0f",weight_lifting_woman:"\ud83c\udfcb\ufe0f&zwj;\u2640\ufe0f",whale:"\ud83d\udc33",whale2:"\ud83d\udc0b",wheel_of_dharma:"\u2638\ufe0f",wheelchair:"\u267f\ufe0f",white_check_mark:"\u2705",white_circle:"\u26aa\ufe0f",white_flag:"\ud83c\udff3\ufe0f",white_flower:"\ud83d\udcae",white_large_square:"\u2b1c\ufe0f",white_medium_small_square:"\u25fd\ufe0f",white_medium_square:"\u25fb\ufe0f",white_small_square:"\u25ab\ufe0f",white_square_button:"\ud83d\udd33",wilted_flower:"\ud83e\udd40",wind_chime:"\ud83c\udf90",wind_face:"\ud83c\udf2c",wine_glass:"\ud83c\udf77",wink:"\ud83d\ude09",wolf:"\ud83d\udc3a",woman:"\ud83d\udc69",woman_artist:"\ud83d\udc69&zwj;\ud83c\udfa8",woman_astronaut:"\ud83d\udc69&zwj;\ud83d\ude80",woman_cartwheeling:"\ud83e\udd38&zwj;\u2640\ufe0f",woman_cook:"\ud83d\udc69&zwj;\ud83c\udf73",woman_facepalming:"\ud83e\udd26&zwj;\u2640\ufe0f",woman_factory_worker:"\ud83d\udc69&zwj;\ud83c\udfed",woman_farmer:"\ud83d\udc69&zwj;\ud83c\udf3e",woman_firefighter:"\ud83d\udc69&zwj;\ud83d\ude92",woman_health_worker:"\ud83d\udc69&zwj;\u2695\ufe0f",woman_judge:"\ud83d\udc69&zwj;\u2696\ufe0f",woman_juggling:"\ud83e\udd39&zwj;\u2640\ufe0f",woman_mechanic:"\ud83d\udc69&zwj;\ud83d\udd27",woman_office_worker:"\ud83d\udc69&zwj;\ud83d\udcbc",woman_pilot:"\ud83d\udc69&zwj;\u2708\ufe0f",woman_playing_handball:"\ud83e\udd3e&zwj;\u2640\ufe0f",woman_playing_water_polo:"\ud83e\udd3d&zwj;\u2640\ufe0f",woman_scientist:"\ud83d\udc69&zwj;\ud83d\udd2c",woman_shrugging:"\ud83e\udd37&zwj;\u2640\ufe0f",woman_singer:"\ud83d\udc69&zwj;\ud83c\udfa4",woman_student:"\ud83d\udc69&zwj;\ud83c\udf93",woman_teacher:"\ud83d\udc69&zwj;\ud83c\udfeb",woman_technologist:"\ud83d\udc69&zwj;\ud83d\udcbb",woman_with_turban:"\ud83d\udc73&zwj;\u2640\ufe0f",womans_clothes:"\ud83d\udc5a",womans_hat:"\ud83d\udc52",women_wrestling:"\ud83e\udd3c&zwj;\u2640\ufe0f",womens:"\ud83d\udeba",world_map:"\ud83d\uddfa",worried:"\ud83d\ude1f",wrench:"\ud83d\udd27",writing_hand:"\u270d\ufe0f",x:"\u274c",yellow_heart:"\ud83d\udc9b",yen:"\ud83d\udcb4",yin_yang:"\u262f\ufe0f",yum:"\ud83d\ude0b",zap:"\u26a1\ufe0f",zipper_mouth_face:"\ud83e\udd10",zzz:"\ud83d\udca4",octocat:'<img alt=":octocat:" height="20" width="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:"<span style=\"font-family: 'Anonymous Pro', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;\">S</span>"},w.Converter=function(a){var n={},s=[],i=[],l={},t=g,o={parsed:{},raw:"",format:""};function c(e,r){if(r=r||null,w.helper.isString(e)){if(r=e=w.helper.stdExtName(e),w.extensions[e])return void function o(e,r){"function"==typeof e&&(e=e(new w.Converter));w.helper.isArray(e)||(e=[e]);var a=f(e,r);if(!a.valid)throw Error(a.error);for(var t=0;t<e.length;++t)switch(e[t].type){case"lang":s.push(e[t]);break;case"output":i.push(e[t]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}(w.extensions[e],e);if(w.helper.isUndefined(h[e]))throw Error('Extension "'+e+'" could not be loaded. It was either not found or is not a valid extension.');e=h[e]}"function"==typeof e&&(e=e()),w.helper.isArray(e)||(e=[e]);var a=f(e,r);if(!a.valid)throw Error(a.error);for(var t=0;t<e.length;++t){switch(e[t].type){case"lang":s.push(e[t]);break;case"output":i.push(e[t])}if(e[t].hasOwnProperty("listeners"))for(var n in e[t].listeners)e[t].listeners.hasOwnProperty(n)&&u(n,e[t].listeners[n])}}function u(e,r){if(!w.helper.isString(e))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+p(e)+" given");if("function"!=typeof r)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+p(r)+" given");l.hasOwnProperty(e)||(l[e]=[]),l[e].push(r)}!function d(){for(var e in a=a||{},_)_.hasOwnProperty(e)&&(n[e]=_[e]);{if("object"!==p(a))throw Error("Converter expects the passed parameter to be an object, but "+p(a)+" was passed instead.");for(var r in a)a.hasOwnProperty(r)&&(n[r]=a[r])}n.extensions&&w.helper.forEach(n.extensions,c)}(),this._dispatch=function(e,r,a,t){if(l.hasOwnProperty(e))for(var n=0;n<l[e].length;++n){var o=l[e][n](e,r,this,a,t);o&&void 0!==o&&(r=o)}return r},this.listen=function(e,r){return u(e,r),this},this.makeHtml=function(r){if(!r)return r;var a={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:s,outputModifiers:i,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return r=(r=(r=(r=(r=r.replace(/\xa8/g,"\xa8T")).replace(/\$/g,"\xa8D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/\u00A0/g,"&nbsp;"),n.smartIndentationFix&&(r=function t(e){var r=e.match(/^\s*/)[0].length,a=new RegExp("^\\s{0,"+r+"}","gm");return e.replace(a,"")}(r)),r="\n\n"+r+"\n\n",r=(r=w.subParser("detab")(r,n,a)).replace(/^[ \t]+$/gm,""),w.helper.forEach(s,function(e){r=w.subParser("runExtension")(e,r,n,a)}),r=w.subParser("metadata")(r,n,a),r=w.subParser("hashPreCodeTags")(r,n,a),r=w.subParser("githubCodeBlocks")(r,n,a),r=w.subParser("hashHTMLBlocks")(r,n,a),r=w.subParser("hashCodeTags")(r,n,a),r=w.subParser("stripLinkDefinitions")(r,n,a),r=w.subParser("blockGamut")(r,n,a),r=w.subParser("unhashHTMLSpans")(r,n,a),r=(r=(r=w.subParser("unescapeSpecialChars")(r,n,a)).replace(/\xa8D/g,"$$")).replace(/\xa8T/g,"\xa8"),r=w.subParser("completeHTMLDocument")(r,n,a),w.helper.forEach(i,function(e){r=w.subParser("runExtension")(e,r,n,a)}),o=a.metadata,r},this.makeMarkdown=this.makeMd=function(e,r){if(e=(e=(e=e.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/>[ \t]+</,">\xa8NBSP;<"),!r){if(!window||!window.document)throw new Error("HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM");r=window.document}var a=r.createElement("div");a.innerHTML=e;var t={preList:function c(e){for(var r=e.querySelectorAll("pre"),a=[],t=0;t<r.length;++t)if(1===r[t].childElementCount&&"code"===r[t].firstChild.tagName.toLowerCase()){var n=r[t].firstChild.innerHTML.trim(),o=r[t].firstChild.getAttribute("data-language")||"";if(""===o)for(var s=r[t].firstChild.className.split(" "),i=0;i<s.length;++i){var l=s[i].match(/^language-(.+)$/);if(null!==l){o=l[1];break}}n=w.helper.unescapeHTMLEntities(n),a.push(n),r[t].outerHTML='<precode language="'+o+'" precodenum="'+t.toString()+'"></precode>'}else a.push(r[t].innerHTML),r[t].innerHTML="",r[t].setAttribute("prenum",t.toString());return a}(a)};!function i(e){for(var r=0;r<e.childNodes.length;++r){var a=e.childNodes[r];3===a.nodeType?/\S/.test(a.nodeValue)?(a.nodeValue=a.nodeValue.split("\n").join(" "),a.nodeValue=a.nodeValue.replace(/(\s)+/g,"$1")):(e.removeChild(a),--r):1===a.nodeType&&i(a)}}(a);for(var n=a.childNodes,o="",s=0;s<n.length;s++)o+=w.subParser("makeMarkdown.node")(n[s],t);return o},this.setOption=function(e,r){n[e]=r},this.getOption=function(e){return n[e]},this.getOptions=function(){return n},this.addExtension=function(e,r){c(e,r=r||null)},this.useExtension=function(e){c(e)},this.setFlavor=function(e){if(!m.hasOwnProperty(e))throw Error(e+" flavor was not found");var r=m[e];for(var a in t=e,r)r.hasOwnProperty(a)&&(n[a]=r[a])},this.getFlavor=function(){return t},this.removeExtension=function(e){w.helper.isArray(e)||(e=[e]);for(var r=0;r<e.length;++r){for(var a=e[r],t=0;t<s.length;++t)s[t]===a&&s[t].splice(t,1);for(;0<i.length;++t)i[0]===a&&i[0].splice(t,1)}},this.getAllExtensions=function(){return{language:s,output:i}},this.getMetadata=function(e){return e?o.raw:o.parsed},this.getMetadataFormat=function(){return o.format},this._setMetadataPair=function(e,r){o.parsed[e]=r},this._setMetadataFormat=function(e){o.format=e},this._setMetadataRaw=function(e){o.raw=e}},w.subParser("anchors",function(e,l,c){var u=function u(e,r,a,t,n,o,s){if(w.helper.isUndefined(s)&&(s=""),a=a.toLowerCase(),-1<e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m))t="";else if(!t){if(a||(a=r.toLowerCase().replace(/ ?\n/g," ")),t="#"+a,w.helper.isUndefined(c.gUrls[a]))return e;t=c.gUrls[a],w.helper.isUndefined(c.gTitles[a])||(s=c.gTitles[a])}var i='<a href="'+(t=t.replace(w.helper.regexes.asteriskDashAndColon,w.helper.escapeCharactersCallback))+'"';return""!==s&&null!==s&&(i+=' title="'+(s=(s=s.replace(/"/g,"&quot;")).replace(w.helper.regexes.asteriskDashAndColon,w.helper.escapeCharactersCallback))+'"'),l.openLinksInNewWindow&&!/^#/.test(t)&&(i+=' rel="noopener noreferrer" target="\xa8E95Eblank"'),i+=">"+r+"</a>"};return e=(e=(e=(e=(e=c.converter._dispatch("anchors.before",e,l,c)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,u)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,u)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,u)).replace(/\[([^\[\]]+)]()()()()()/g,u),l.ghMentions&&(e=e.replace(/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d.-]+?[a-z\d]+)*))/gim,function(e,r,a,t,n){if("\\"===a)return r+t;if(!w.helper.isString(l.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");var o=l.ghMentionsLink.replace(/\{u}/g,n),s="";return l.openLinksInNewWindow&&(s=' rel="noopener noreferrer" target="\xa8E95Eblank"'),r+'<a href="'+o+'"'+s+">"+t+"</a>"})),e=c.converter._dispatch("anchors.after",e,l,c)});var n=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+?\.[^'">\s]+?)()(\1)?(?=\s|$)(?!["<>])/gi,s=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]])?(\1)?(?=\s|$)(?!["<>])/gi,i=/()<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>()/gi,l=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim,c=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi,u=function u(p){return function(e,r,a,t,n,o,s){var i=a=a.replace(w.helper.regexes.asteriskDashAndColon,w.helper.escapeCharactersCallback),l="",c="",u=r||"",d=s||"";return/^www\./i.test(a)&&(a=a.replace(/^www\./i,"http://www.")),p.excludeTrailingPunctuationFromURLs&&o&&(l=o),p.openLinksInNewWindow&&(c=' rel="noopener noreferrer" target="\xa8E95Eblank"'),u+'<a href="'+a+'"'+c+">"+i+"</a>"+l+d}},d=function d(n,o){return function(e,r,a){var t="mailto:";return r=r||"",a=w.subParser("unescapeSpecialChars")(a,n,o),n.encodeEmails?(t=w.helper.encodeEmailAddress(t+a),a=w.helper.encodeEmailAddress(a)):t+=a,r+'<a href="'+t+'">'+a+"</a>"}};return w.subParser("autoLinks",function(e,r,a){return e=(e=(e=a.converter._dispatch("autoLinks.before",e,r,a)).replace(i,u(r))).replace(c,d(r,a)),e=a.converter._dispatch("autoLinks.after",e,r,a)}),w.subParser("simplifiedAutoLinks",function(e,r,a){return r.simplifiedAutoLink?(e=a.converter._dispatch("simplifiedAutoLinks.before",e,r,a),e=(e=r.excludeTrailingPunctuationFromURLs?e.replace(s,u(r)):e.replace(n,u(r))).replace(l,d(r,a)),e=a.converter._dispatch("simplifiedAutoLinks.after",e,r,a)):e}),w.subParser("blockGamut",function(e,r,a){return e=a.converter._dispatch("blockGamut.before",e,r,a),e=w.subParser("blockQuotes")(e,r,a),e=w.subParser("headers")(e,r,a),e=w.subParser("horizontalRule")(e,r,a),e=w.subParser("lists")(e,r,a),e=w.subParser("codeBlocks")(e,r,a),e=w.subParser("tables")(e,r,a),e=w.subParser("hashHTMLBlocks")(e,r,a),e=w.subParser("paragraphs")(e,r,a),e=a.converter._dispatch("blockGamut.after",e,r,a)}),w.subParser("blockQuotes",function(e,r,a){e=a.converter._dispatch("blockQuotes.before",e,r,a),e+="\n\n";var t=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return r.splitAdjacentBlockquotes&&(t=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),e=e.replace(t,function(e){return e=(e=(e=e.replace(/^[ \t]*>[ \t]?/gm,"")).replace(/\xa80/g,"")).replace(/^[ \t]+$/gm,""),e=w.subParser("githubCodeBlocks")(e,r,a),e=(e=(e=w.subParser("blockGamut")(e,r,a)).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,function(e,r){var a=r;return a=(a=a.replace(/^ {2}/gm,"\xa80")).replace(/\xa80/g,"")}),w.subParser("hashBlock")("<blockquote>\n"+e+"\n</blockquote>",r,a)}),e=a.converter._dispatch("blockQuotes.after",e,r,a)}),w.subParser("codeBlocks",function(e,s,i){e=i.converter._dispatch("codeBlocks.before",e,s,i);return e=(e=(e+="\xa80").replace(/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=\xa80))/g,function(e,r,a){var t=r,n=a,o="\n";return t=w.subParser("outdent")(t,s,i),t=w.subParser("encodeCode")(t,s,i),t=(t=(t=w.subParser("detab")(t,s,i)).replace(/^\n+/g,"")).replace(/\n+$/g,""),s.omitExtraWLInCodeBlocks&&(o=""),t="<pre><code>"+t+o+"</code></pre>",w.subParser("hashBlock")(t,s,i)+n})).replace(/\xa80/,""),e=i.converter._dispatch("codeBlocks.after",e,s,i)}),w.subParser("codeSpans",function(e,o,s){return void 0===(e=s.converter._dispatch("codeSpans.before",e,o,s))&&(e=""),e=e.replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,function(e,r,a,t){var n=t;return n=(n=n.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""),n=r+"<code>"+(n=w.subParser("encodeCode")(n,o,s))+"</code>",n=w.subParser("hashHTMLSpans")(n,o,s)}),e=s.converter._dispatch("codeSpans.after",e,o,s)}),w.subParser("completeHTMLDocument",function(e,r,a){if(!r.completeHTMLDocument)return e;e=a.converter._dispatch("completeHTMLDocument.before",e,r,a);var t="html",n="<!DOCTYPE HTML>\n",o="",s='<meta charset="utf-8">\n',i="",l="";for(var c in"undefined"!=typeof a.metadata.parsed.doctype&&(n="<!DOCTYPE "+a.metadata.parsed.doctype+">\n","html"!==(t=a.metadata.parsed.doctype.toString().toLowerCase())&&"html5"!==t||(s='<meta charset="utf-8">')),a.metadata.parsed)if(a.metadata.parsed.hasOwnProperty(c))switch(c.toLowerCase()){case"doctype":break;case"title":o="<title>"+a.metadata.parsed.title+"</title>\n";break;case"charset":s="html"===t||"html5"===t?'<meta charset="'+a.metadata.parsed.charset+'">\n':'<meta name="charset" content="'+a.metadata.parsed.charset+'">\n';break;case"language":case"lang":i=' lang="'+a.metadata.parsed[c]+'"',l+='<meta name="'+c+'" content="'+a.metadata.parsed[c]+'">\n';break;default:l+='<meta name="'+c+'" content="'+a.metadata.parsed[c]+'">\n'}return e=n+"<html"+i+">\n<head>\n"+o+s+l+"</head>\n<body>\n"+e.trim()+"\n</body>\n</html>",e=a.converter._dispatch("completeHTMLDocument.after",e,r,a)}),w.subParser("detab",function(e,r,a){return e=(e=(e=(e=(e=(e=a.converter._dispatch("detab.before",e,r,a)).replace(/\t(?=\t)/g,"    ")).replace(/\t/g,"\xa8A\xa8B")).replace(/\xa8B(.+?)\xa8A/g,function(e,r){for(var a=r,t=4-a.length%4,n=0;n<t;n++)a+=" ";return a})).replace(/\xa8A/g,"    ")).replace(/\xa8B/g,""),e=a.converter._dispatch("detab.after",e,r,a)}),w.subParser("ellipsis",function(e,r,a){return e=(e=a.converter._dispatch("ellipsis.before",e,r,a)).replace(/\.\.\./g,"\u2026"),e=a.converter._dispatch("ellipsis.after",e,r,a)}),w.subParser("emoji",function(e,r,a){if(!r.emoji)return e;return e=(e=a.converter._dispatch("emoji.before",e,r,a)).replace(/:([\S]+?):/g,function(e,r){return w.helper.emojis.hasOwnProperty(r)?w.helper.emojis[r]:e}),e=a.converter._dispatch("emoji.after",e,r,a)}),w.subParser("encodeAmpsAndAngles",function(e,r,a){return e=(e=(e=(e=(e=a.converter._dispatch("encodeAmpsAndAngles.before",e,r,a)).replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?$!])/gi,"&lt;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),e=a.converter._dispatch("encodeAmpsAndAngles.after",e,r,a)}),w.subParser("encodeBackslashEscapes",function(e,r,a){return e=(e=(e=a.converter._dispatch("encodeBackslashEscapes.before",e,r,a)).replace(/\\(\\)/g,w.helper.escapeCharactersCallback)).replace(/\\([`*_{}\[\]()>#+.!~=|-])/g,w.helper.escapeCharactersCallback),e=a.converter._dispatch("encodeBackslashEscapes.after",e,r,a)}),w.subParser("encodeCode",function(e,r,a){return e=(e=a.converter._dispatch("encodeCode.before",e,r,a)).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,w.helper.escapeCharactersCallback),e=a.converter._dispatch("encodeCode.after",e,r,a)}),w.subParser("escapeSpecialCharsWithinTagAttributes",function(e,r,a){return e=(e=(e=a.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",e,r,a)).replace(/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,function(e){return e.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,w.helper.escapeCharactersCallback)})).replace(/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi,function(e){return e.replace(/([\\`*_~=|])/g,w.helper.escapeCharactersCallback)}),e=a.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",e,r,a)}),w.subParser("githubCodeBlocks",function(e,o,s){return o.ghCodeBlocks?(e=s.converter._dispatch("githubCodeBlocks.before",e,o,s),e=(e=(e+="\xa80").replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,function(e,r,a,t){var n=o.omitExtraWLInCodeBlocks?"":"\n";return t=w.subParser("encodeCode")(t,o,s),t="<pre><code"+(a?' class="'+a+" language-"+a+'"':"")+">"+(t=(t=(t=w.subParser("detab")(t,o,s)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+n+"</code></pre>",t=w.subParser("hashBlock")(t,o,s),"\n\n\xa8G"+(s.ghCodeBlocks.push({text:e,codeblock:t})-1)+"G\n\n"})).replace(/\xa80/,""),s.converter._dispatch("githubCodeBlocks.after",e,o,s)):e}),w.subParser("hashBlock",function(e,r,a){return e=(e=a.converter._dispatch("hashBlock.before",e,r,a)).replace(/(^\n+|\n+$)/g,""),e="\n\n\xa8K"+(a.gHtmlBlocks.push(e)-1)+"K\n\n",e=a.converter._dispatch("hashBlock.after",e,r,a)}),w.subParser("hashCodeTags",function(e,o,s){e=s.converter._dispatch("hashCodeTags.before",e,o,s);var i=function i(e,r,a,t){var n=a+w.subParser("encodeCode")(r,o,s)+t;return"\xa8C"+(s.gHtmlSpans.push(n)-1)+"C"};return e=w.helper.replaceRecursiveRegExp(e,i,"<code\\b[^>]*>","</code>","gim"),e=s.converter._dispatch("hashCodeTags.after",e,o,s)}),w.subParser("hashElement",function(e,r,t){return function(e,r){var a=r;return a=(a=(a=a.replace(/\n\n/g,"\n")).replace(/^\n/,"")).replace(/\n+$/g,""),a="\n\n\xa8K"+(t.gHtmlBlocks.push(a)-1)+"K\n\n"}}),w.subParser("hashHTMLBlocks",function(e,r,o){e=o.converter._dispatch("hashHTMLBlocks.before",e,r,o);var a=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"],s=function s(e,r,a,t){var n=e;return-1!==a.search(/\bmarkdown\b/)&&(n=a+o.converter.makeHtml(r)+t),"\n\n\xa8K"+(o.gHtmlBlocks.push(n)-1)+"K\n\n"};r.backslashEscapesHTMLTags&&(e=e.replace(/\\<(\/?[^>]+?)>/g,function(e,r){return"&lt;"+r+"&gt;"}));for(var t=0;t<a.length;++t)for(var n,i=new RegExp("^ {0,3}(<"+a[t]+"\\b[^>]*>)","im"),l="<"+a[t]+"\\b[^>]*>",c="</"+a[t]+">";-1!==(n=w.helper.regexIndexOf(e,i));){var u=w.helper.splitAtIndex(e,n),d=w.helper.replaceRecursiveRegExp(u[1],s,l,c,"im");if(d===u[1])break;e=u[0].concat(d)}return e=e.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,w.subParser("hashElement")(e,r,o)),e=(e=w.helper.replaceRecursiveRegExp(e,function(e){return"\n\n\xa8K"+(o.gHtmlBlocks.push(e)-1)+"K\n\n"},"^ {0,3}\x3c!--","--\x3e","gm")).replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,w.subParser("hashElement")(e,r,o)),e=o.converter._dispatch("hashHTMLBlocks.after",e,r,o)}),w.subParser("hashHTMLSpans",function(e,r,a){function t(e){return"\xa8C"+(a.gHtmlSpans.push(e)-1)+"C"}return e=(e=(e=(e=(e=a.converter._dispatch("hashHTMLSpans.before",e,r,a)).replace(/<[^>]+?\/>/gi,function(e){return t(e)})).replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,function(e){return t(e)})).replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,function(e){return t(e)})).replace(/<[^>]+?>/gi,function(e){return t(e)}),e=a.converter._dispatch("hashHTMLSpans.after",e,r,a)}),w.subParser("unhashHTMLSpans",function(e,r,a){e=a.converter._dispatch("unhashHTMLSpans.before",e,r,a);for(var t=0;t<a.gHtmlSpans.length;++t){for(var n=a.gHtmlSpans[t],o=0;/\xa8C(\d+)C/.test(n);){var s=RegExp.$1;if(n=n.replace("\xa8C"+s+"C",a.gHtmlSpans[s]),10===o)break;++o}e=e.replace("\xa8C"+t+"C",n)}return e=a.converter._dispatch("unhashHTMLSpans.after",e,r,a)}),w.subParser("hashPreCodeTags",function(e,o,s){e=s.converter._dispatch("hashPreCodeTags.before",e,o,s);var i=function i(e,r,a,t){var n=a+w.subParser("encodeCode")(r,o,s)+t;return"\n\n\xa8G"+(s.ghCodeBlocks.push({text:e,codeblock:n})-1)+"G\n\n"};return e=w.helper.replaceRecursiveRegExp(e,i,"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),e=s.converter._dispatch("hashPreCodeTags.after",e,o,s)}),w.subParser("headers",function(e,l,c){e=c.converter._dispatch("headers.before",e,l,c);var u=isNaN(parseInt(l.headerLevelStart))?1:parseInt(l.headerLevelStart),r=l.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,a=l.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;e=(e=e.replace(r,function(e,r){var a=w.subParser("spanGamut")(r,l,c),t=l.noHeaderId?"":' id="'+d(r)+'"',n="<h"+u+t+">"+a+"</h"+u+">";return w.subParser("hashBlock")(n,l,c)})).replace(a,function(e,r){var a=w.subParser("spanGamut")(r,l,c),t=l.noHeaderId?"":' id="'+d(r)+'"',n=u+1,o="<h"+n+t+">"+a+"</h"+n+">";return w.subParser("hashBlock")(o,l,c)});var t=l.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm;function d(e){var r,a;if(l.customizedHeaderId){var t=e.match(/\{([^{]+?)}\s*$/);t&&t[1]&&(e=t[1])}return r=e,a=w.helper.isString(l.prefixHeaderId)?l.prefixHeaderId:!0===l.prefixHeaderId?"section-":"",l.rawPrefixHeaderId||(r=a+r),r=l.ghCompatibleHeaderId?r.replace(/ /g,"-").replace(/&amp;/g,"").replace(/\xa8T/g,"").replace(/\xa8D/g,"").replace(/[&+$,\/:;=?@"#{}|^\xa8~\[\]`\\*)(%.!'<>]/g,"").toLowerCase():l.rawHeaderId?r.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/\xa8T/g,"\xa8").replace(/\xa8D/g,"$").replace(/["']/g,"-").toLowerCase():r.replace(/[^\w]/g,"").toLowerCase(),l.rawPrefixHeaderId&&(r=a+r),c.hashLinkCounts[r]?r=r+"-"+c.hashLinkCounts[r]++:c.hashLinkCounts[r]=1,r}return e=e.replace(t,function(e,r,a){var t=a;l.customizedHeaderId&&(t=a.replace(/\s?\{([^{]+?)}\s*$/,""));var n=w.subParser("spanGamut")(t,l,c),o=l.noHeaderId?"":' id="'+d(a)+'"',s=u-1+r.length,i="<h"+s+o+">"+n+"</h"+s+">";return w.subParser("hashBlock")(i,l,c)}),e=c.converter._dispatch("headers.after",e,l,c)}),w.subParser("horizontalRule",function(e,r,a){e=a.converter._dispatch("horizontalRule.before",e,r,a);var t=w.subParser("hashBlock")("<hr />",r,a);return e=(e=(e=e.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,t)).replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,t)).replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,t),e=a.converter._dispatch("horizontalRule.after",e,r,a)}),w.subParser("images",function(e,r,p){function l(e,r,a,t,n,o,s,i){var l=p.gUrls,c=p.gTitles,u=p.gDimensions;if(a=a.toLowerCase(),i||(i=""),-1<e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m))t="";else if(""===t||null===t){if(""!==a&&null!==a||(a=r.toLowerCase().replace(/ ?\n/g," ")),t="#"+a,w.helper.isUndefined(l[a]))return e;t=l[a],w.helper.isUndefined(c[a])||(i=c[a]),w.helper.isUndefined(u[a])||(n=u[a].width,o=u[a].height)}r=r.replace(/"/g,"&quot;").replace(w.helper.regexes.asteriskDashAndColon,w.helper.escapeCharactersCallback);var d='<img src="'+(t=t.replace(w.helper.regexes.asteriskDashAndColon,w.helper.escapeCharactersCallback))+'" alt="'+r+'"';return i&&w.helper.isString(i)&&(d+=' title="'+(i=i.replace(/"/g,"&quot;").replace(w.helper.regexes.asteriskDashAndColon,w.helper.escapeCharactersCallback))+'"'),n&&o&&(d+=' width="'+(n="*"===n?"auto":n)+'"',d+=' height="'+(o="*"===o?"auto":o)+'"'),d+=" />"}return e=(e=(e=(e=(e=(e=p.converter._dispatch("images.before",e,r,p)).replace(/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,l)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,function c(e,r,a,t,n,o,s,i){return l(e,r,a,t=t.replace(/\s/g,""),n,o,0,i)})).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,l)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,l)).replace(/!\[([^\[\]]+)]()()()()()/g,l),e=p.converter._dispatch("images.after",e,r,p)}),w.subParser("italicsAndBold",function(e,r,a){function t(e,r,a){return r+e+a}return e=a.converter._dispatch("italicsAndBold.before",e,r,a),e=r.literalMidWordUnderscores?(e=(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,function(e,r){return t(r,"<strong><em>","</em></strong>")})).replace(/\b__(\S[\s\S]*?)__\b/g,function(e,r){return t(r,"<strong>","</strong>")})).replace(/\b_(\S[\s\S]*?)_\b/g,function(e,r){return t(r,"<em>","</em>")}):(e=(e=e.replace(/___(\S[\s\S]*?)___/g,function(e,r){return/\S$/.test(r)?t(r,"<strong><em>","</em></strong>"):e})).replace(/__(\S[\s\S]*?)__/g,function(e,r){return/\S$/.test(r)?t(r,"<strong>","</strong>"):e})).replace(/_([^\s_][\s\S]*?)_/g,function(e,r){return/\S$/.test(r)?t(r,"<em>","</em>"):e}),e=r.literalMidWordAsterisks?(e=(e=e.replace(/([^*]|^)\B\*\*\*(\S[\s\S]*?)\*\*\*\B(?!\*)/g,function(e,r,a){return t(a,r+"<strong><em>","</em></strong>")})).replace(/([^*]|^)\B\*\*(\S[\s\S]*?)\*\*\B(?!\*)/g,function(e,r,a){return t(a,r+"<strong>","</strong>")})).replace(/([^*]|^)\B\*(\S[\s\S]*?)\*\B(?!\*)/g,function(e,r,a){return t(a,r+"<em>","</em>")}):(e=(e=e.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,function(e,r){return/\S$/.test(r)?t(r,"<strong><em>","</em></strong>"):e})).replace(/\*\*(\S[\s\S]*?)\*\*/g,function(e,r){return/\S$/.test(r)?t(r,"<strong>","</strong>"):e})).replace(/\*([^\s*][\s\S]*?)\*/g,function(e,r){return/\S$/.test(r)?t(r,"<em>","</em>"):e}),e=a.converter._dispatch("italicsAndBold.after",e,r,a)}),w.subParser("lists",function(e,p,u){function h(e,r){u.gListLevel++,e=e.replace(/\n{2,}$/,"\n");var a=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(\xa80| {0,3}([*+-]|\d+[.])[ \t]+))/gm,c=/\n[ \t]*\n(?!\xa80)/.test(e+="\xa80");return p.disableForced4SpacesIndentedSublists&&(a=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(\xa80|\2([*+-]|\d+[.])[ \t]+))/gm),e=(e=e.replace(a,function(e,r,a,t,n,o,s){s=s&&""!==s.trim();var i=w.subParser("outdent")(n,p,u),l="";return o&&p.tasklists&&(l=' class="task-list-item" style="list-style-type: none;"',i=i.replace(/^[ \t]*\[(x|X| )?]/m,function(){var e='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return s&&(e+=" checked"),e+=">"})),i=i.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,function(e){return"\xa8A"+e}),i="<li"+l+">"+(i=(i=r||-1<i.search(/\n{2,}/)?(i=w.subParser("githubCodeBlocks")(i,p,u),w.subParser("blockGamut")(i,p,u)):(i=(i=w.subParser("lists")(i,p,u)).replace(/\n$/,""),i=(i=w.subParser("hashHTMLBlocks")(i,p,u)).replace(/\n\n+/g,"\n\n"),c?w.subParser("paragraphs")(i,p,u):w.subParser("spanGamut")(i,p,u))).replace("\xa8A",""))+"</li>\n"})).replace(/\xa80/g,""),u.gListLevel--,r&&(e=e.replace(/\s+$/,"")),e}function _(e,r){if("ol"===r){var a=e.match(/^ *(\d+)\./);if(a&&"1"!==a[1])return' start="'+a[1]+'"'}return""}function g(e){return"ul"===e?' style="list-style: disc !important;padding: 0px 0px 0px 40px !important;"':' style="list-style: decimal !important;padding: 0px 0px 0px 40px !important;"'}function n(t,n,o){var s=p.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,i=p.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,l="ul"===n?s:i,c="",u=g(n);if(-1!==t.search(l))!function d(e){var r=e.search(l),a=_(t,n);u=g(n),-1!==r?(c+="\n\n<"+n+u+a+">\n"+h(e.slice(0,r),!!o)+"</"+n+">\n",l="ul"===(n="ul"===n?"ol":"ul")?s:i,d(e.slice(r))):c+="\n\n<"+n+u+a+">\n"+h(e,!!o)+"</"+n+">\n"}(t);else{var e=_(t,n);c="\n\n<"+n+u+e+">\n"+h(t,!!o)+"</"+n+">\n"}return c}return e=u.converter._dispatch("lists.before",e,p,u),e+="\xa80",e=(e=u.gListLevel?e.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(\xa80|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(e,r,a){return n(r,-1<a.search(/[*+-]/g)?"ul":"ol",!0)}):e.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(\xa80|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,function(e,r,a,t){return n(a,-1<t.search(/[*+-]/g)?"ul":"ol",!1)})).replace(/\xa80/,""),e=u.converter._dispatch("lists.after",e,p,u)}),w.subParser("metadata",function(e,r,t){if(!r.metadata)return e;function n(e){(e=(e=(t.metadata.raw=e).replace(/&/g,"&amp;").replace(/"/g,"&quot;")).replace(/\n {4}/g," ")).replace(/^([\S ]+): +([\s\S]+?)$/gm,function(e,r,a){return t.metadata.parsed[r]=a,""})}return e=(e=(e=(e=t.converter._dispatch("metadata.before",e,r,t)).replace(/^\s*\xab\xab\xab+(\S*?)\n([\s\S]+?)\n\xbb\xbb\xbb+\n/,function(e,r,a){return n(a),"\xa8M"})).replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,function(e,r,a){return r&&(t.metadata.format=r),n(a),"\xa8M"})).replace(/\xa8M/g,""),e=t.converter._dispatch("metadata.after",e,r,t)}),w.subParser("outdent",function(e,r,a){return e=(e=(e=a.converter._dispatch("outdent.before",e,r,a)).replace(/^(\t|[ ]{1,4})/gm,"\xa80")).replace(/\xa80/g,""),e=a.converter._dispatch("outdent.after",e,r,a)}),w.subParser("paragraphs",function(e,r,a){for(var t=(e=(e=(e=a.converter._dispatch("paragraphs.before",e,r,a)).replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),n=[],o=t.length,s=0;s<o;s++){var i=t[s];0<=i.search(/\xa8(K|G)(\d+)\1/g)?n.push(i):0<=i.search(/\S/)&&(i=(i=w.subParser("spanGamut")(i,r,a)).replace(/^([ \t]*)/g,"<p>"),i+="</p>",n.push(i))}for(o=n.length,s=0;s<o;s++){for(var l="",c=n[s],u=!1;/\xa8(K|G)(\d+)\1/.test(c);){var d=RegExp.$1,p=RegExp.$2;l=(l="K"===d?a.gHtmlBlocks[p]:u?w.subParser("encodeCode")(a.ghCodeBlocks[p].text,r,a):a.ghCodeBlocks[p].codeblock).replace(/\$/g,"$$$$"),c=c.replace(/(\n\n)?\xa8(K|G)\d+\2(\n\n)?/,l),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(c)&&(u=!0)}n[s]=c}return e=(e=(e=n.join("\n")).replace(/^\n+/g,"")).replace(/\n+$/g,""),a.converter._dispatch("paragraphs.after",e,r,a)}),w.subParser("runExtension",function(e,r,a,t){if(e.filter)r=e.filter(r,t.converter,a);else if(e.regex){var n=e.regex;n instanceof RegExp||(n=new RegExp(n,"g")),r=r.replace(n,e.replace)}return r}),w.subParser("spanGamut",function(e,r,a){return e=a.converter._dispatch("spanGamut.before",e,r,a),e=w.subParser("codeSpans")(e,r,a),e=w.subParser("escapeSpecialCharsWithinTagAttributes")(e,r,a),e=w.subParser("encodeBackslashEscapes")(e,r,a),e=w.subParser("images")(e,r,a),e=w.subParser("anchors")(e,r,a),e=w.subParser("autoLinks")(e,r,a),e=w.subParser("simplifiedAutoLinks")(e,r,a),e=w.subParser("emoji")(e,r,a),e=w.subParser("underline")(e,r,a),e=w.subParser("italicsAndBold")(e,r,a),e=w.subParser("strikethrough")(e,r,a),e=w.subParser("ellipsis")(e,r,a),e=w.subParser("hashHTMLSpans")(e,r,a),e=w.subParser("encodeAmpsAndAngles")(e,r,a),r.simpleLineBreaks?/\n\n\xa8K/.test(e)||(e=e.replace(/\n+/g,"<br />\n")):e=e.replace(/  +\n/g,"<br />\n"),e=a.converter._dispatch("spanGamut.after",e,r,a)}),w.subParser("strikethrough",function(e,t,n){return t.strikethrough&&(e=(e=n.converter._dispatch("strikethrough.before",e,t,n)).replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,function(e,r){return function a(e){return t.simplifiedAutoLink&&(e=w.subParser("simplifiedAutoLinks")(e,t,n)),"<del>"+e+"</del>"}(r)}),e=n.converter._dispatch("strikethrough.after",e,t,n)),e}),w.subParser("stripLinkDefinitions",function(e,i,l){var c=function c(e,r,a,t,n,o,s){return r=r.toLowerCase(),a.match(/^data:.+?\/.+?;base64,/)?l.gUrls[r]=a.replace(/\s/g,""):l.gUrls[r]=w.subParser("encodeAmpsAndAngles")(a,i,l),o?o+s:(s&&(l.gTitles[r]=s.replace(/"|'/g,"&quot;")),i.parseImgDimensions&&t&&n&&(l.gDimensions[r]={width:t,height:n}),"")};return e=(e=(e=(e+="\xa80").replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=\xa80)|(?=\n\[))/gm,c)).replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=\xa80))/gm,c)).replace(/\xa80/,"")}),w.subParser("tables",function(e,f,b){if(!f.tables)return e;function r(e){var r,a=e.split("\n");for(r=0;r<a.length;++r)/^ {0,3}\|/.test(a[r])&&(a[r]=a[r].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(a[r])&&(a[r]=a[r].replace(/\|[ \t]*$/,"")),a[r]=w.subParser("codeSpans")(a[r],f,b);var t,n,o,s,i,l=a[0].split("|").map(function(e){return e.trim()}),c=a[1].split("|").map(function(e){return e.trim()}),u=[],d=[],p=[],h=[];for(a.shift(),a.shift(),r=0;r<a.length;++r)""!==a[r].trim()&&u.push(a[r].split("|").map(function(e){return e.trim()}));if(l.length<c.length)return e;for(r=0;r<c.length;++r)p.push((t=c[r],/^:[ \t]*--*$/.test(t)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(t)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(t)?' style="text-align:center;"':""));for(r=0;r<l.length;++r)w.helper.isUndefined(p[r])&&(p[r]=""),d.push((n=l[r],o=p[r],s=void 0,s="",n=n.trim(),(f.tablesHeaderId||f.tableHeaderId)&&(s=' id="'+n.replace(/ /g,"_").toLowerCase()+'"'),"<th"+s+o+">"+(n=w.subParser("spanGamut")(n,f,b))+"</th>\n"));for(r=0;r<u.length;++r){for(var _=[],g=0;g<d.length;++g)w.helper.isUndefined(u[r][g]),_.push((i=u[r][g],"<td"+p[g]+">"+w.subParser("spanGamut")(i,f,b)+"</td>\n"));h.push(_)}return function m(e,r){for(var a="<table>\n<thead>\n<tr>\n",t=e.length,n=0;n<t;++n)a+=e[n];for(a+="</tr>\n</thead>\n<tbody>\n",n=0;n<r.length;++n){a+="<tr>\n";for(var o=0;o<t;++o)a+=r[n][o];a+="</tr>\n"}return a+="</tbody>\n</table>\n"}(d,h)}return e=(e=(e=(e=b.converter._dispatch("tables.before",e,f,b)).replace(/\\(\|)/g,w.helper.escapeCharactersCallback)).replace(/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|\xa80)/gm,r)).replace(/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|\xa80)/gm,r),e=b.converter._dispatch("tables.after",e,f,b)}),w.subParser("underline",function(e,r,a){return r.underline?(e=a.converter._dispatch("underline.before",e,r,a),e=(e=r.literalMidWordUnderscores?(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,function(e,r){return"<u>"+r+"</u>"})).replace(/\b__(\S[\s\S]*?)__\b/g,function(e,r){return"<u>"+r+"</u>"}):(e=e.replace(/___(\S[\s\S]*?)___/g,function(e,r){return/\S$/.test(r)?"<u>"+r+"</u>":e})).replace(/__(\S[\s\S]*?)__/g,function(e,r){return/\S$/.test(r)?"<u>"+r+"</u>":e})).replace(/(_)/g,w.helper.escapeCharactersCallback),e=a.converter._dispatch("underline.after",e,r,a)):e}),w.subParser("unescapeSpecialChars",function(e,r,a){return e=(e=a.converter._dispatch("unescapeSpecialChars.before",e,r,a)).replace(/\xa8E(\d+)E/g,function(e,r){var a=parseInt(r);return String.fromCharCode(a)}),e=a.converter._dispatch("unescapeSpecialChars.after",e,r,a)}),w.subParser("makeMarkdown.blockquote",function(e,r){var a="";if(e.hasChildNodes())for(var t=e.childNodes,n=t.length,o=0;o<n;++o){var s=w.subParser("makeMarkdown.node")(t[o],r);""!==s&&(a+=s)}return a="> "+(a=a.trim()).split("\n").join("\n> ")}),w.subParser("makeMarkdown.codeBlock",function(e,r){var a=e.getAttribute("language"),t=e.getAttribute("precodenum");return"```"+a+"\n"+r.preList[t]+"\n```"}),w.subParser("makeMarkdown.codeSpan",function(e){return"`"+e.innerHTML+"`"}),w.subParser("makeMarkdown.emphasis",function(e,r){var a="";if(e.hasChildNodes()){a+="*";for(var t=e.childNodes,n=t.length,o=0;o<n;++o)a+=w.subParser("makeMarkdown.node")(t[o],r);a+="*"}return a}),w.subParser("makeMarkdown.header",function(e,r,a){var t=new Array(a+1).join("#"),n="";if(e.hasChildNodes()){n=t+" ";for(var o=e.childNodes,s=o.length,i=0;i<s;++i)n+=w.subParser("makeMarkdown.node")(o[i],r)}return n}),w.subParser("makeMarkdown.hr",function(){return"---"}),w.subParser("makeMarkdown.image",function(e){var r="";return e.hasAttribute("src")&&(r+="!["+e.getAttribute("alt")+"](",r+="<"+e.getAttribute("src")+">",e.hasAttribute("width")&&e.hasAttribute("height")&&(r+=" ="+e.getAttribute("width")+"x"+e.getAttribute("height")),e.hasAttribute("title")&&(r+=' "'+e.getAttribute("title")+'"'),r+=")"),r}),w.subParser("makeMarkdown.links",function(e,r){var a="";if(e.hasChildNodes()&&e.hasAttribute("href")){var t=e.childNodes,n=t.length;a="[";for(var o=0;o<n;++o)a+=w.subParser("makeMarkdown.node")(t[o],r);a+="](",a+="<"+e.getAttribute("href")+">",e.hasAttribute("title")&&(a+=' "'+e.getAttribute("title")+'"'),a+=")"}return a}),w.subParser("makeMarkdown.list",function(e,r,a){var t="";if(!e.hasChildNodes())return"";for(var n=e.childNodes,o=n.length,s=e.getAttribute("start")||1,i=0;i<o;++i)if("undefined"!=typeof n[i].tagName&&"li"===n[i].tagName.toLowerCase()){t+=("ol"===a?s.toString()+". ":"- ")+w.subParser("makeMarkdown.listItem")(n[i],r),++s}return(t+="\n\x3c!-- --\x3e\n").trim()}),w.subParser("makeMarkdown.listItem",function(e,r){for(var a="",t=e.childNodes,n=t.length,o=0;o<n;++o)a+=w.subParser("makeMarkdown.node")(t[o],r);return/\n$/.test(a)?a=a.split("\n").join("\n    ").replace(/^ {4}$/gm,"").replace(/\n\n+/g,"\n\n"):a+="\n",a}),w.subParser("makeMarkdown.node",function(e,r,a){a=a||!1;var t="";if(3===e.nodeType)return w.subParser("makeMarkdown.txt")(e,r);if(8===e.nodeType)return"\x3c!--"+e.data+"--\x3e\n\n";if(1!==e.nodeType)return"";switch(e.tagName.toLowerCase()){case"h1":a||(t=w.subParser("makeMarkdown.header")(e,r,1)+"\n\n");break;case"h2":a||(t=w.subParser("makeMarkdown.header")(e,r,2)+"\n\n");break;case"h3":a||(t=w.subParser("makeMarkdown.header")(e,r,3)+"\n\n");break;case"h4":a||(t=w.subParser("makeMarkdown.header")(e,r,4)+"\n\n");break;case"h5":a||(t=w.subParser("makeMarkdown.header")(e,r,5)+"\n\n");break;case"h6":a||(t=w.subParser("makeMarkdown.header")(e,r,6)+"\n\n");break;case"p":a||(t=w.subParser("makeMarkdown.paragraph")(e,r)+"\n\n");break;case"blockquote":a||(t=w.subParser("makeMarkdown.blockquote")(e,r)+"\n\n");break;case"hr":a||(t=w.subParser("makeMarkdown.hr")(e,r)+"\n\n");break;case"ol":a||(t=w.subParser("makeMarkdown.list")(e,r,"ol")+"\n\n");break;case"ul":a||(t=w.subParser("makeMarkdown.list")(e,r,"ul")+"\n\n");break;case"precode":a||(t=w.subParser("makeMarkdown.codeBlock")(e,r)+"\n\n");break;case"pre":a||(t=w.subParser("makeMarkdown.pre")(e,r)+"\n\n");break;case"table":a||(t=w.subParser("makeMarkdown.table")(e,r)+"\n\n");break;case"code":t=w.subParser("makeMarkdown.codeSpan")(e,r);break;case"em":case"i":t=w.subParser("makeMarkdown.emphasis")(e,r);break;case"strong":case"b":t=w.subParser("makeMarkdown.strong")(e,r);break;case"del":t=w.subParser("makeMarkdown.strikethrough")(e,r);break;case"a":t=w.subParser("makeMarkdown.links")(e,r);break;case"img":t=w.subParser("makeMarkdown.image")(e,r);break;default:t=e.outerHTML+"\n\n"}return t}),w.subParser("makeMarkdown.paragraph",function(e,r){var a="";if(e.hasChildNodes())for(var t=e.childNodes,n=t.length,o=0;o<n;++o)a+=w.subParser("makeMarkdown.node")(t[o],r);return a=a.trim()}),w.subParser("makeMarkdown.pre",function(e,r){var a=e.getAttribute("prenum");return"<pre>"+r.preList[a]+"</pre>"}),w.subParser("makeMarkdown.strikethrough",function(e,r){var a="";if(e.hasChildNodes()){a+="~~";for(var t=e.childNodes,n=t.length,o=0;o<n;++o)a+=w.subParser("makeMarkdown.node")(t[o],r);a+="~~"}return a}),w.subParser("makeMarkdown.strong",function(e,r){var a="";if(e.hasChildNodes()){a+="**";for(var t=e.childNodes,n=t.length,o=0;o<n;++o)a+=w.subParser("makeMarkdown.node")(t[o],r);a+="**"}return a}),w.subParser("makeMarkdown.table",function(e,r){var a,t,n="",o=[[],[]],s=e.querySelectorAll("thead>tr>th"),i=e.querySelectorAll("tbody>tr");for(a=0;a<s.length;++a){var l=w.subParser("makeMarkdown.tableCell")(s[a],r),c="---";if(s[a].hasAttribute("style"))switch(s[a].getAttribute("style").toLowerCase().replace(/\s/g,"")){case"text-align:left;":c=":---";break;case"text-align:right;":c="---:";break;case"text-align:center;":c=":---:"}o[0][a]=l.trim(),o[1][a]=c}for(a=0;a<i.length;++a){var u=o.push([])-1,d=i[a].getElementsByTagName("td");for(t=0;t<s.length;++t){var p=" ";"undefined"!=typeof d[t]&&(p=w.subParser("makeMarkdown.tableCell")(d[t],r)),o[u].push(p)}}var h=3;for(a=0;a<o.length;++a)for(t=0;t<o[a].length;++t){var _=o[a][t].length;h<_&&(h=_)}for(a=0;a<o.length;++a){for(t=0;t<o[a].length;++t)1===a?":"===o[a][t].slice(-1)?o[a][t]=w.helper.padEnd(o[a][t].slice(-1),h-1,"-")+":":o[a][t]=w.helper.padEnd(o[a][t],h,"-"):o[a][t]=w.helper.padEnd(o[a][t],h);n+="| "+o[a].join(" | ")+" |\n"}return n.trim()}),w.subParser("makeMarkdown.tableCell",function(e,r){var a="";if(!e.hasChildNodes())return"";for(var t=e.childNodes,n=t.length,o=0;o<n;++o)a+=w.subParser("makeMarkdown.node")(t[o],r,!0);return a.trim()}),w.subParser("makeMarkdown.txt",function(e){var r=e.nodeValue;return r=(r=r.replace(/ +/g," ")).replace(/\xa8NBSP;/g," "),r=(r=(r=(r=(r=(r=(r=(r=(r=w.helper.unescapeHTMLEntities(r)).replace(/([*_~|`])/g,"\\$1")).replace(/^(\s*)>/g,"\\$1>")).replace(/^#/gm,"\\#")).replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3")).replace(/^( {0,3}\d+)\./gm,"$1\\.")).replace(/^( {0,3})([+-])/gm,"$1\\$2")).replace(/]([\s]*)\(/g,"\\]$1\\(")).replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:")}),w});
Django==4.2.7
django-froala-editor
whitenoise
django-taggit

python manage.py runserver

<PERSON><PERSON><PERSON>


<!-- <div class="our-features-item">
Our Features Image Start -->
<div class="our-features-image">
<figure class="image-anime reveal" style="height: 580px;">
<img alt="<PERSON><PERSON><PERSON><PERSON>aiser - Teacher at Holistic Foster Care Foundation" src="https://i.postimg.cc/GhfPryZf/Whats-App-Image-2025-04-14-at-4-57-13-PM.jpg"/>
</figure>
</div>
<!-- Our Features Image End -->
<!-- Our Features Content Start -->
<div class="our-features-content">
<div class="our-features-body">
<h2>Inspiring Young Minds</h2>
<h3><PERSON><PERSON><PERSON>aiser</h3>
<p>
                 <PERSON><PERSON><PERSON>, a passionate volunteer at Holistic Foster Care Foundation (HFCF), champions gender equality by empowering young girls to overcome challenges and reach their full potential. She is dedicated to helping them develop confidence and dignity. Separately, <PERSON> is noted for driving financial excellence at HFCF.
                </p>
</div>
<div class="icon-box">
<img alt="Teacher icon" src="{% static 'images/icon-our-features-3.svg' %}"/>
</div>

</div>


<!-- Our Features Content End -->
</div>

style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover;"